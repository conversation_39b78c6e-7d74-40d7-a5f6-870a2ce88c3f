SELECT
    *
FROM
    (
        SELECT
            tsw.f_order_man,--工单接单人
            tsw.f_handlingtype,--工单类型
            tsw.f_service_id, --工单编号
            tcp.f_userinfo_code,
            tcp.f_user_name,
            tud.f_slice_area,
            tcp.f_user_phone,
            tcp.f_residential_area,
            tcp.f_address,
            tcp.f_checker_name,
            tcp.f_offsite_time,
            tcp.f_filialeid,
            tpd.f_device_type,
            tdi.f_item_name,
            tdil.f_item_value,
            tdi.f_path,
            case when tdi.f_is_repaired = '已处理' THEN '已处理' else tdi.f_deal_dispose end as f_deal_dispose,
            tdi.f_longitude,
            tdi.f_latitude,
            tcp.f_upload_date,
            tcp.f_check_type,
            tcp.id as f_paper_id,
            tdi.id as f_defect_id,
            tbb.f_gas_date,
            CASE

                WHEN tdi.f_defect_level IS NULL
                    OR tdi.f_defect_level= '' THEN
                    '未定级' ELSE tdi.f_defect_level
                END f_defect_level,
            CASE
                WHEN tdi.f_is_defect != 'true' THEN '无隐患'
                WHEN tdi.f_is_defect = 'true' AND (tdi.f_user_changes = '是' or tdi.f_deal_dispose in ('自行处理', '自行整改')) THEN '用户自行整改'
                WHEN tdi.f_is_defect = 'true' AND (tdi.f_live_dispose = '是' or tdi.f_deal_dispose='现场整改') THEN '现场已处理'
                WHEN tdi.f_is_defect = 'true' AND tdi.f_is_repaired = '已处理' THEN '维修已处理'
                WHEN tdi.f_is_defect = 'true' AND tdi.f_is_repaired = '转维修' THEN '转维修'
                ELSE '隐患未处理'
                END f_is_repaired,
            CASE
                WHEN tdi.f_is_defect = 'true' AND (tdi.f_live_dispose = '是' or tdi.f_deal_dispose='现场整改') THEN tcp.f_checker_name
                WHEN tdi.f_repairman IS NULL THEN '***' ELSE tdi.f_repairman
                END f_repairman,
            CASE
                WHEN tdi.f_is_defect = 'true' AND (tdi.f_live_dispose = '是' or tdi.f_deal_dispose='现场整改') THEN tcp.f_offsite_time
                WHEN tdi.f_repair_date IS NULL THEN '****-**-** **:**:**' ELSE tdi.f_repair_date
                END f_repair_date,
            tpim.f_meter_base,--已用气量/金额/字轮数
            tpim.f_aliasname,--表型号
            tpim.f_flow_direction,--表方向
            case when tpim.f_collection_type = '按气量' then tpim.f_total_gas else tpim.f_total_fee end as f_total,--累购金额/累购气量
            ROUND((case when tpim.f_collection_type = '按气量' then tpim.f_total_gas else tpim.f_total_fee end) - tpim.f_meter_base,2) as f_residue--剩余金额/气量
        FROM
            t_check_paper tcp
                LEFT JOIN t_paper_devices tpd ON tcp.id= tpd.f_paper_id
                LEFT JOIN t_user_address tud  WITH ( nolock )  ON tcp.f_userinfoid= tud.f_userinfo_id
                LEFT JOIN t_devices_items tdi ON tpd.id= tdi.f_device_id
                LEFT JOIN t_devices_items_lists tdil ON tdil.f_item_id= tdi.id
                LEFT JOIN t_user usr ON usr.id = tcp.f_checker_id
                left join t_plan_item_meter tpim on tcp.f_check_item_id = tpim.f_plan_item_id
                LEFT JOIN t_serviceworkorder   tsw ON tsw.f_paper_id = tcp.id
                LEFT JOIN ( SELECT f_userinfo_id,f_gas_date FROM t_userfiles  WITH ( nolock ) WHERE f_table_state = '正常' or f_table_state = '停用' ) tbb ON tcp.f_userinfoid= tbb.f_userinfo_id
        WHERE
                tdi.f_is_defect = 'true'
        ) tt where { condition }
ORDER BY
    f_offsite_time DESC
