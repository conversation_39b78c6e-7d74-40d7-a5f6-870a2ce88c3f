select * from (
      select F_USERINFOID,
             F_USER_NAME,
             F_USER_PHONE,
             F_ADDRESS,
             F_NOANSWER_PATH  as F_DAOFANG1_PATH,
             F_NOANSWER1_PATH as F_DAOFANG2_PATH,
             F_NOANSWER2_PATH as F_DAOFANG3_PATH,
             F_CHECKER_NAME,
             F_ONSITE_TIME as F_UPLOAD_DATE,
             F_REMARK         as F_COMMENTS
      from T_CHECK_PAPER
      where F_ENTRY_STATUS = '到访不遇'
      union all
      select '' as F_USERINFOID,
             '' as F_USER_NAME,
             '' as F_USER_PHONE,
             f_address,
             F_DAOFANG1_PATH,
             F_DAOFANG2_PATH,
             F_DAOFANG3_PATH,
             F_CHECKER_NAME,
             F_UPLOAD_DATE,
             F_COMMENTS
      from T_VISIT_PAPER
  ) t
where {condition}
order by F_UPLOAD_DATE desc