with rcq AS
(
	select id, name, parentid, ename from (
		select name, id, parentid, '' ename from t_organization
		union all
		select name, id, parentid, ename from t_user where id != {id}) t where  t.id = {org_id}
	UNION ALL
	select a.id id, a.name name, a.parentid parentid, a.ename ename from (
		select name, id, parentid, '' ename from t_organization
		union all
		select name, id, parentid, ename from t_user where id != {id}) a
	INNER JOIN rcq s on s.id = a.parentid
)

select t1.f_checker, task, isnull(accomplished,0) accomplished from 
(
select p.f_checker f_checker, count(*) task from t_check_plan p inner join t_check_plan_item i on p.id = i.f_plan_id 
where p.f_plan_year = {f_plan_year} and p.f_plan_month = {f_plan_month} and p.f_subcompany='{f_subcompany}' and p.f_checker in (select name from rcq where ename != '') 
group by p.f_checker
) t1 left join 
(
select f_checker_name f_checker, count(*) accomplished from t_check_paper 
where f_subcompany='{f_subcompany}' and f_checker_name in (select name from rcq where ename != '') 
and SUBSTRING(f_offsite_time, 1, 7) = '{f_ym}' 
and f_approved='已审核'  
group by f_checker_name
) t2
on t1.f_checker = t2.f_checker