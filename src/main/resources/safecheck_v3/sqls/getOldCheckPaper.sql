SELECT
        ( CASE WHEN f_ruhu = 1 THEN '入户'  ELSE '拒检' END ) f_entry_status,
        f_userid,
        f_consumername,
        f_consumerphone,
        f_residential_area,
        f_address,
        f_anjianriqi,
        f_state,
        f_checker_name,
        f_onsite_time,
        f_offsite_time,
        f_bi<PERSON><PERSON>,
        f_kachang<PERSON>a,
        f_meter_type,
        f_rqbiaoxing,
        f_shengyu,
        f_buygas,
        f_baojingqi,
        f_baojingqichang,
        (CASE WHEN f_rshqshiyong = 1 THEN '使用' ELSE '无' end) f_rshqshiyong,
        f_rshqpinpai,
        f_rshqxinghao,
        f_rshqxianzhuang,
        (CASE WHEN f_bglshiyong = 1 THEN '使用' ELSE '无' end) f_bglshiyong,
        f_bglpinpai,
        f_bglweizhi,
        f_bglxianzhuang,
        (CASE WHEN f_zjshiyong = 1 THEN '使用' ELSE '无' end) f_zjshiyong,
        f_zjpinpa,
        f_zjleixing,
        (CASE WHEN f_lgzhengchang = 1 THEN '正常' ELSE '不正常' end) f_lgzhengchang,
        (CASE WHEN f_lgfushi = 1 THEN '腐蚀' ELSE '' end) f_lgfushi,
        (CASE WHEN f_lgsigai = 1 THEN '私改' ELSE '' end) f_lgsigai,
        (CASE WHEN f_lglouqi = 1 THEN '漏气' ELSE '' end) f_lglouqi,
        (CASE WHEN f_sibiao = 1 THEN '死表' ELSE '' end) f_sibiao,
        (CASE WHEN f_changtong = 1 THEN '长通' ELSE '' end) f_changtong,
        (CASE WHEN f_fanzhuang = 1 THEN '反装' ELSE '' end) f_fanzhuang,
        (CASE WHEN f_qblouqi = 1 THEN '漏气' ELSE '' end) f_qblouqi,
        f_kehupingjia
    FROM
        t_oldcheck_paper
    WHERE
        {condition}
