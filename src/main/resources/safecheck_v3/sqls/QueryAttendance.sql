	with rcq AS
	(
		select id, name, parentid, ename from (
			select name, id, parentid, '' ename from t_organization
			union all
			select name, id, parentid, ename from t_user) t where  t.id = {org_id}
		UNION ALL
		select a.id id, a.name name, a.parentid parentid, a.ename ename from (
			select name, id, parentid, '' ename from t_organization
			union all
			select name, id, parentid, ename from t_user) a
		INNER JOIN rcq s on s.id = a.parentid
	)

	select * from t_user_signin where f_date = '{f_date}' and f_userinfoid in (select id from rcq where ename != '')
