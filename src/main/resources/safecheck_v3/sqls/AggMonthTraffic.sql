${
__n__ == $__n__$ :
$
	with rcq AS
	(
		select id, name, parentid, ename from (
			select name, id, parentid, '' ename from t_organization
			union all
			select name, id, parentid, ename from t_user ) t where  t.id = {f_loggedOnOrgId}
		UNION ALL
		select a.id id, a.name name, a.parentid parentid, a.ename ename from (
			select name, id, parentid, '' ename from t_organization
			union all
			select name, id, parentid, ename from t_user ) a
		INNER JOIN rcq s on s.id = a.parentid
	)
	select {f_aggregations} from (select f_user_name from t_phone_state where f_period='天' and SUBSTRING(f_datetime, 1, 7) = '{f_ym}' and f_userinfoid in (select id as value from rcq where ename!='')
	group by f_user_name) t
$,
$
	with rcq AS
	(
		select id, name, parentid, ename from (
			select name, id, parentid, '' ename from t_organization
			union all
			select name, id, parentid, ename from t_user ) t where  t.id = {f_loggedOnOrgId}
		UNION ALL
		select a.id id, a.name name, a.parentid parentid, a.ename ename from (
			select name, id, parentid, '' ename from t_organization
			union all
			select name, id, parentid, ename from t_user ) a
		INNER JOIN rcq s on s.id = a.parentid
	)
	select * from (
	select *, ROW_NUMBER() OVER (ORDER BY f_userinfoid) rownum from (
		select f_userinfoid, f_user_name, round(sum(f_traffic)/1024.0/1024.0, 2, 2) f_traffic from t_phone_state where f_period='天' and SUBSTRING(f_datetime, 1, 7) = '{f_ym}' and f_userinfoid in (select id as value from rcq where ename!='')
		group by f_userinfoid, f_user_name
	) t ) tt where rownum between {__startRow__} and {__endRow__}
$
}
