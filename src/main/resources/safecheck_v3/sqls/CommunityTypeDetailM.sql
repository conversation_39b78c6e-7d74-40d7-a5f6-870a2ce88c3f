SELECT
    tcp.f_residential_area,
    sum( 1 ) as yhhs,
    sum( case when b.wcl != b.a and b.yclyh != b.a then 1 else 0 end )  as clzyh,
    sum( 1 ) - sum( case when b.wcl != b.a and b.yclyh != b.a then 1 else 0 end ) - sum( case when b.yclyh = b.a then 1 else 0 end ) as wcl,
    sum( case when b.yclyh = b.a then 1 else 0 end )  as yclyh,
    isnull(sum(ljcount),0) AS ljcount,
    isnull(sum(jgcount),0) AS jgcount,
    isnull(sum(rqjcount),0) AS rqjcount
FROM
    t_check_paper tcp
        LEFT JOIN (
        SELECT
            tcp.id,
            count( tdi.id ) AS a,
            sum(case when f_item_value = '漏气' THEN 1 ELSE 0 END ) AS ljcount,
            sum(case when f_item_value = '胶管' THEN 1 ELSE 0 END ) AS jgcount,
            sum(case when f_item_value = '燃气具老化' THEN 1 ELSE 0 END ) AS rqjcount,
            sum( CASE WHEN tdi.f_live_dispose='是' or tdi.f_is_repaired in ('已处理') or tdi.f_deal_dispose in ('现场整改') THEN 1 ELSE 0 END ) AS yclyh,
            sum( CASE WHEN tdi.f_deal_dispose in ('未处理','报修','自行处理','自行整改') and f_repaired != '已派维修单' THEN 1 ELSE 0 END ) AS wcl,
            sum( CASE WHEN f_repaired = '已派维修单' AND f_repaired_uploaded != '已传' THEN 1 ELSE 0 END ) AS clzyh
        FROM
            t_check_paper tcp
                LEFT JOIN ( SELECT f_userinfoid, max( f_upload_date ) f_upload_date FROM t_check_paper WHERE f_entry_status = '入户' AND f_defect_content LIKE '%有隐患%' GROUP BY f_userinfoid ) tcp2 ON tcp.f_userinfoid = tcp2.f_userinfoid
                AND tcp.f_upload_date = tcp2.f_upload_date
                LEFT JOIN t_paper_devices tpd ON tcp.id = tpd.f_paper_id
                LEFT JOIN t_devices_items tdi ON tpd.id = tdi.f_device_id
        WHERE
                1 = 1
          AND f_is_defect = 'true'
        GROUP BY
            tcp.id
    ) b ON tcp.id = b.id
WHERE {condition} and tcp.f_filialeid = {orgid} and tcp.f_check_type = '民用' and f_defect_content like '%有隐患%'
GROUP BY
    tcp.f_residential_area

