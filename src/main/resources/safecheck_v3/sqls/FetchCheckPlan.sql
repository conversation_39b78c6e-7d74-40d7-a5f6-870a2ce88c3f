select cp.id id, f_plan_name, f_plan_year, f_plan_month, 
count(*) cnt, sum(case when f_approved='已审核' then 1 else 0 end) verifiedCnt,
sum(case when f_last_check_state = '入户' then 1 else 0 end) centry,
sum(case when f_last_check_state = '拒检' then 1 else 0 end) cdeny,
sum(case when f_last_check_state = '到访不遇' then 1 else 0 end) cabsent 
from t_check_plan cp left join t_check_plan_item  cpi on cp.id=cpi.f_plan_id 
where cp.f_checker ='{f_checker}' and cp.f_checker_id ='{f_checker_id}' and f_plan_year={f_plan_year} and f_issued='是'
group by cp.id, f_plan_name, f_plan_year, f_plan_month
order by  f_plan_year, f_plan_month
