--民用日现金气量核对
--month：月份
--return：日期，居民气量，实际金额，户数，用户气量单价
select * from t_userfiles where f_checker='{f_checker}' and f_user_type != '居民' and  f_subcompany='{f_subcompany}' and cast(substring(convert(varchar(20),DATEADD(YEAR,1,f_last_check_date),120), 1, 7) as varchar(30))='{plan_year_month}'
	union
 select * from t_userfiles where f_checker='{f_checker}' and f_user_type = '居民' and f_subcompany='{f_subcompany}' and cast(substring(convert(varchar(20),DATEADD(YEAR,2,f_last_check_date),120), 1, 7) as varchar(30))='{plan_year_month}'
