SELECT ss.f_device_type,count(DISTINCT(ss.f_device_type)) safenum
from
(

SELECT tpd.f_device_type,tdi.f_item_value,tdi.f_defect_level,count(*) hiddennum
from
t_check_paper tp,
t_paper_devices tpd,
t_devices_items tdi
WHERE tp.id=tpd.f_paper_id and tpd.id=tdi.f_device_id
and tdi.f_is_defect='true'
{
  f_filialeid!=$$:
  $ and tp.f_filialeid in {f_filialeid}$,
  $$
}
{
startDate!=$$:$ and  tp.f_offsite_time>='{startDate}'$,$$
}
{
endDate!=$$:$  and  tp.f_offsite_time<='{endDate}'$,$$
}
GROUP BY tpd.f_device_type,tdi.f_item_value,tdi.f_defect_level


)ss

group  by ss.f_device_type