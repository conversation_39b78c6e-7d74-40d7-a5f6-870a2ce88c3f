SELECT
    CONVERT(VARCHAR, tcp.f_plan_year) + '合计' as f_plan_year,
    isnull(COUNT(0),0) AS data1,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_state = '未检' and tcpi.f_last_check_state is null THEN 1 ELSE 0 END),0) AS data2,
    isnull(tcp2.data8,0) AS data11,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state in ('入户') OR (tcpi.f_last_check_state = '到访不遇' and tcpi.f_check_version >= 3) THEN 1 ELSE 0 END),0) AS data3,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '入户' THEN 1 ELSE 0 END),0) + isnull(tcp2.data8,0) AS data12,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '入户' THEN 1 ELSE 0 END),0) AS data4,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '到访不遇' and tcpi.f_check_version = 1 THEN 1 ELSE 0 END),0) AS data5,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '到访不遇' and tcpi.f_check_version = 2 THEN 1 ELSE 0 END),0) AS data6,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '到访不遇' and tcpi.f_check_version >= 3 THEN 1 ELSE 0 END),0) AS data7,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '到访不遇' THEN 1 ELSE 0 END),0) AS data8,
    isnull(SUM(CASE WHEN tcpi.f_upload_state='已传' and tcpi.f_last_check_state = '拒检' THEN 1 ELSE 0 END),0) AS data9
FROM
    t_check_plan_item tcpi
        LEFT JOIN t_check_plan tcp ON tcpi.f_plan_id = tcp.id
        left join (SELECT year(f_offsite_time) as year,count(0) AS data8 FROM t_check_paper WHERE {condition.condition2} and f_no_checkplan='无计划安检' GROUP BY year(f_offsite_time)) tcp2 on tcp.f_plan_year = tcp2.year
WHERE
    tcp.f_checker IS NOT NULL and {condition.condition1} and tcp.f_plan_year is not null
GROUP BY
    tcp.f_plan_year,tcp2.data8
order by tcp.f_plan_year
