SELECT
    isnull(sum( check_count ),0)  AS check_count,
    isnull(f_residential_area,'') as f_residential_area,
    isnull(f_checker_name,'') as f_checker_name,
    isnull(sum( check_ok_num ),0) AS check_ok_num,
    isnull(sum( check_no_ok_num ),0) AS check_no_ok_num,
    isnull(sum( handle_num ),0) AS handle_num,
    isnull(sum( no_handle_num ),0) AS no_handle_num,
    CASE WHEN SUM( handle_num ) > 0 THEN (CAST(CAST(SUM( handle_num ) * 1.0 * 100 / (sum( handle_num ) + sum( no_handle_num )) AS DECIMAL ( 10, 2 )) AS VARCHAR ( 50 )) + '%') ELSE '0%' END AS handle_percentage
FROM
    (
        SELECT
            count( tcp.id ) AS check_count,
            f_residential_area,
            f_checker_name,
            sum( CASE WHEN f_defect_content LIKE '%正常%' THEN 1 ELSE 0 END ) AS check_ok_num,
            sum( CASE WHEN f_defect_content NOT LIKE '%正常%' THEN 1 ELSE 0 END ) AS check_no_ok_num,
            (SELECT sum( CASE WHEN f_deal_dispose IN ( '报修', '现场整改' ) THEN 1 ELSE 0 END ) FROM t_devices_items tdi LEFT JOIN t_paper_devices tpd ON tdi.f_device_id = tpd.id WHERE tpd.f_paper_id = tcp.id and f_is_defect = 'true' ) AS handle_num,
            (SELECT sum( CASE WHEN f_deal_dispose NOT IN ( '报修', '现场整改' ) THEN 1 ELSE 0 END ) FROM t_devices_items tdi LEFT JOIN t_paper_devices tpd ON tdi.f_device_id = tpd.id WHERE tpd.f_paper_id = tcp.id and f_is_defect = 'true' ) AS no_handle_num
        FROM
            t_check_paper tcp
        WHERE
            {condition } and f_entry_status = '入户'
        GROUP BY
            f_residential_area,
            f_checker_name,
            id
    ) a
GROUP BY
    f_residential_area,
    f_checker_name