SELECT
    f_checker_name AS f_data0,--安检员
    COUNT (*) AS f_data1,--计划总数
    SUM ( CASE WHEN f_entry_status != '入户' THEN 1 ELSE 0 END ) AS f_data2,--计划中未入户
    SUM ( CASE WHEN f_entry_status = '入户' THEN 1 ELSE 0 END ) AS f_data3,--计划中已入户
    SUM ( CASE WHEN f_defect_content LIKE '%有隐患%' THEN 1 ELSE 0 END ) AS f_data4,--存在隐患户数
    CONCAT ( SUM ( CASE WHEN f_entry_status = '入户' THEN 1 ELSE 0 END ) * 100 / COUNT ( 0 ), '%' ) AS f_data5 --安检率
FROM
    t_check_paper
where {condition }
GROUP BY
    f_checker_name