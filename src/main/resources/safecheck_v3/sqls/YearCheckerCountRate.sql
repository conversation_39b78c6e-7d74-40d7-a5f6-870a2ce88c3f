SELECT
    f_checker_id,
    f_checker_name,
    COUNT ( CASE f_entry_status WHEN '入户' THEN f_entry_status ELSE NULL END ) AS rh,
    COUNT ( CASE f_entry_status WHEN '到访不遇' THEN f_entry_status ELSE NULL END ) AS dfby,
    COUNT ( CASE f_entry_status WHEN '拒检' THEN f_entry_status ELSE NULL END ) AS jj,
    COUNT ( CASE f_entry_status WHEN '未使用天然气' THEN f_entry_status ELSE NULL END ) AS wsy
FROM
    (
        SELECT
            u2.f_checker_id,
            u2.f_checker_name,
            u2.f_entry_status
        FROM
            t_check_plan_item u1
                LEFT JOIN (
                SELECT
                    *
                FROM
                    (
                        SELECT
                            *,
                            ROW_NUMBER () OVER ( partition BY f_userinfoid ORDER BY f_offsite_time DESC ) AS lastcheck
                        FROM
                            (
                                SELECT
                                    f_checker_id,
                                    f_checker_name,
                                    f_subcompany,
                                    f_userinfoid,
                                    f_check_item_id,
                                    f_entry_status,
                                    f_safe_type,
                                    f_offsite_time
                                FROM
                                    t_check_paper
                                WHERE
                                        f_check_type = '民用'
                                    { f_start_time == $$ :
					$
					AND 1 = 1 $,
					$
					AND f_onsite_time >= '{f_start_time} 00:00:00' $
					}
					{ f_end_time == $$ :
					$
					AND 1 = 1 $,
					$
					AND f_offsite_time <= '{f_end_time} 23:59:59' $
					}
                            ) tp
                    ) t
                WHERE
                        lastcheck = 1
            ) u2 ON u1.id= u2.f_check_item_id
                LEFT JOIN t_check_plan u3 ON u3.id= u1.f_plan_id
                LEFT JOIN t_userinfo tu ON tu.f_userinfo_id = u1.f_userinfoid
        WHERE
                u1.f_user_type = '民用'
            { orgid != $$ :
    $
		AND u1.f_filialeid IN { orgid } $,
    $ $
    }
    { f_safecheck_type != $$ :
    $
     AND u1.f_safecheck_type = '{ f_safecheck_type }' $,
    $ $
    }
		AND ( tu.f_user_state= '正常' OR tu.f_user_state= '停用' )
		AND
		{ plan_type == $ FALSE $  && f_start_time != $$ :
		$tu.f_open_date < DATENAME( YEAR, '{f_start_time}' ) + '-01-01 00:00:00' $,
		plan_type == $ TRUE $ && f_start_time != $$ :
		$tu.f_open_date > DATENAME( YEAR, '{f_start_time}' ) + '-01-01 00:00:00' $,
		plan_type == $$ :
		$tu.f_open_date < DATENAME(
			YEAR,
		GETDATE()) + '-01-01 00:00:00' $,
		$1 = 1 $
		}
    ) tt
GROUP BY
    f_checker_id,
    f_checker_name
ORDER BY
    1
