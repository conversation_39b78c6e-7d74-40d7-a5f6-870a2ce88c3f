${
__n__ == $__n__$ :
$
	select {f_aggregations} from (
		select * from t_check_plan where
		{
			f_subcompany != $$ :
			$
				f_subcompany='{f_subcompany}' and
			$,
			$
			$
		}
		f_checker=	'{f_checker}' and f_plan_year = {f_plan_year}
	) t
$,
$
	select * from
	(
		select t1.f_plan_year, t1.f_plan_month, task, isnull(accomplished,0) accomplished, ROW_NUMBER() OVER (ORDER BY t1.f_plan_month) AS rownum  from
		(
		select p.f_plan_year, p.f_plan_month, count(*) task from t_check_plan p inner join t_check_plan_item i on p.id = i.f_plan_id where p.f_plan_year = {f_plan_year}
		{
			f_subcompany != $$ :
			$
				and i.f_subcompany='{f_subcompany}'
			$,
			$
			$
		}
		and p.f_checker = '{f_checker}'
		group by p.f_plan_year, p.f_plan_month
		) t1 left join
		(
		select SUBSTRING(f_offsite_time, 1,4) f_plan_year, cast(SUBSTRING(f_offsite_time, 6,2) as int) f_plan_month, count(*) accomplished from t_check_paper
		where
		{
			f_subcompany != $$ :
			$
				f_subcompany='{f_subcompany}' and
			$,
			$
			$
		}
		f_checker_name='{f_checker}'
		and f_offsite_time > '{f_plan_year}-01-01' and f_offsite_time <= '{f_plan_year}-12-31 23:59:59'
		and f_approved='已审核'
		group by SUBSTRING(f_offsite_time, 1,4), cast(SUBSTRING(f_offsite_time, 6,2) as int)
		) t2 on t1.f_plan_year = t2.f_plan_year and t1.f_plan_month = t2.f_plan_month
	) t where rownum between {__startRow__} and {__endRow__}
$
}
