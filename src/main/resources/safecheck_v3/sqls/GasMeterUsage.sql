select p.f_user_name,p.f_userinfoid,c.* from
t_check_paper p 
inner join 
t_check_paper_meter c 
on p.id = c.f_paper_id
 {
 	    date1 != $$ && date2 != $$ && year != $$:
        $
         and p.f_offsite_time >= '{date1}' and p.f_offsite_time <= '{date2} 23:59:59' and
          year(getdate()) -c.f_making_date>={year}
        $,
 	    date1 != $$ && date2 != $$:
 	    $
         and p.f_offsite_time >= '{date1}' and p.f_offsite_time <= '{date2} 23:59:59' 
        $,
        year != $$:
        $
        and year(getdate()) -c.f_making_date>={year}
        $,
        date1 == $$ && date2 == $$ && year == $$:
        $
         and 1=1
        $,
        $$
 	   }
 	   and {condition}
{
	f_subcompany != $$:
	$ and p.f_subcompany='{f_subcompany}'$,
	$$
}
{
	f_check_type == $$:
	$$,
	f_check_type == $居民$:
	$ and p.f_check_type='{f_check_type}'$,
	$ and p.f_check_type!='居民'$
}
{
	f_maker == $$:
	$$,
	$ and c.f_maker='{f_maker}'$
}
and p.f_approved='已审核'
order by p.f_user_name