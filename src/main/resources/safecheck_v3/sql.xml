<cfg>
	<!--*****************************************app*****************************************pc-->
	<!-- 预约安检定时器查询 -->
	<sql alias='YuYueSafecheckQuery' path='YuYueSafecheckQuery.sql' mobile='true'/>
	<!-- 提取安检计划 -->
	<sql alias='提取安检计划' path='FetchCheckPlan.sql' mobile='true'/>
	<!-- 过滤安检项-->
	<sql alias='过滤安检项' path='FilterCheckItems.sql' mobile='true'/>
	<!-- 提取安检单  安卓端使用-->
	<sql alias='提取安检单' path='FetchACheckPaper.sql'  mobile='true'/>
	<!-- 手机端用户信息查询-->
	<sql alias='androidGetuserinfo' path='AndroidGetUserInfo.sql' mobile='true' />
	<sql alias='androidGetuserinfoOnline' path='AndroidGetUserInfoOnline.sql'  />
	<sql alias='getCheckPlanItemByPlanId' path='getCheckPlanItemByPlanId.sql' />
	<sql alias='getCheckPlanMeterByPlanId' path='getCheckPlanMeterByPlanId.sql' />
	<!-- 手机端已办安检单信息查询-->
	<sql alias='androidGetHasDone' path='AndroidGetHasDone.sql' mobile='true' />

	<!-- 手机端用户信息查询-->
	<sql alias='AndroidGetCheckPlanResult' path='AndroidGetCheckPlanResult.sql' mobile='true' />
	<!-- 计划小区分组 -->
	<sql alias='计划小区分组' path='GroupPlanProgressByResidentialArea.sql' mobile='true'/>
	<sql alias='计划按小区导航' path='PlanItemsByAreaAndYM.sql' mobile='true'/>
	<sql alias='计划按预约导航' path='PlanItemsByPrearrange.sql' mobile='true'/>
	<sql alias='提取安检通知' path='FetchNoticeinfo.sql' mobile='true'/>
	<sql alias='GetNewPlan' path='GetNewPlan.sql' mobile='true'/>
	<!-- 手机端已办巡检信息查询-->
	<sql alias='androidGetInsHasDone' path='AndroidGetInsHasDone.sql' mobile='true'/>

	<!--*****************************************pc*****************************************pc-->
	<!-- 登录 -->
	<sql alias='登录' path='LoginAndGetOrgHierarchy.sql' />
	<!-- 查找登录用户信息  -->
	<sql alias='查找登录用户信息' path='FindLoggedInUser.sql' />
	<!-- 获取用户签到状态 -->
	<sql alias='获取签到状态' path='GetDailySignInOffInfo.sql' />
	<!-- 获取新通知  -->
	<sql alias='检查新通知' path='FetchNewNotices.sql' />
	<!-- 检查新计划  -->
	<sql alias='检查新计划' path='FetchNewPlans.sql' />
	<!-- 检查新维修  -->
	<sql alias='检查新维修' path='FetchNewRepairs.sql' />
	<!-- 提取通知 -->
	<sql alias='提取通知' path='FetchNotice.sql' />
	<!-- 维修单列表 -->
	<sql alias='维修单列表' path='FilterRepairPaper.sql' />
	<!-- 根据密码查找用户-->
	<sql alias='根据密码查找用户' path='FindUserByPassAndId.sql' />
	<!-- 公告查询 -->
	<sql alias='公告查询' path='FetchAllNotices.sql' />
	<!-- 电话本 汪-->
	<sql alias='communiCation' path='communiCation.sql'/>
	<sql alias='用户档案审核查询' path='VerifyUserFileQuery.sql' />
	<!-- 用户档案查询-->
	<sql alias='用户档案查询' path='FetchUserFileByOrgArchiveDate.sql' />
	<!-- 预约计划-->
	<sql alias='根据安检员查询用户档案' path='FetchUserFileByOrgChecker.sql' />
	<sql alias='searchSafeRecordByUserinfo' path='searchSafeRecordByUserinfo.sql' />
	<!-- 预约计划 未安检筛选 且 已生成计划-->
	<!-- 预约计划 未安检筛选 -->
	<sql alias='FetchUserFileByOrgNoRuHuSXQ' path='FetchUserFileByOrgNoRuHuSXQ.sql' />
	<!--	<sql alias='FetchUserFileByOrgNoRuHuSXQ1' path='FetchUserFileByOrgNoRuHuSXQ1.sql' />-->
	<!-- 预约计划 未安检筛选 且 没有生成计划-->
	<sql alias='FetchUserFileByOrgNoSafeSX' path='FetchUserFileByOrgNoSafeSX.sql' />
	<!-- 预约计划下发详情-->
	<sql alias='预约计划下发详情' path='bespeakcheckplanitem.sql' />
	<!-- 预约计划下发-->
	<sql alias='预约计划下发' path='bespeakcheckplan.sql' />
	<!-- 安检计划去重-->
	<sql alias='deletecheckplanrepeat' path='DeleteCheckPlanRepeat.sql' />
	<!-- 上传安检单时查询安检单 -->
	<sql alias='checker' path='checkpaerper.sql'/>
	<sql alias="getSealInfo" path="getSealInfo.sql"/>
	<!-- 计划计数 -->
	<sql alias='planCount' path='planCount.sql'/>
	<sql alias='planCountAndNoPLan' path='planCountAndNoPLan.sql'/>
	<!-- 根据年月安检员查询安检项 -->
	<sql alias='根据年月安检员查询安检项' path='FetchPlanItemByYearMonthCheckerId.sql'/>
	<!-- 计划调整页面sql -->
	<sql alias='FetchPlanItemByYearMonthCheckerId' path='FetchPlanItemByYearMonthCheckerId.sql'/>
	<sql alias='根据安检员查询安检项' path='FetchPlanItemByCheckerId.sql'/>
	<!-- 计划项查询 -->
	<sql alias='planItem' path='planItem.sql'/>
	<!--	安检计划-->
	<sql alias='getCheckPlanList' path='getCheckPlanList.sql' />
	<!--计划去重查询-->
	<sql alias='deleteplanrepeat' path='deleteplanrepeat.sql'/>
	<!-- 查找安检员对应的维修员 -->
	<sql alias='查找安检员对应的维修员' path='FetchRepairmanOfChecker.sql'/>
	<!-- 维修小区分组 -->
	<sql alias='维修小区分组' path='GroupPaperByResidentialArea.sql' />
	<sql alias='FindTmpUserById' path='FindTmpUserById.sql'/>
	<sql alias='FindUserById' path='FindUserById.sql'/>
	<!-- 查找安检单-->
	<sql alias='查找安检单' path='QueryCheckPaper.sql' />
	<!-- 查找作废安检单-->
	<sql alias='NoPlanSearchUser' path='NoPlanSearchUser.sql' />
	<!--无计划查询sql	-->
	<sql alias='QueryDelCheckPaper' path='QueryDelCheckPaper.sql' />
	<!-- 查找巡检单-->
	<sql alias='queryInspection' path='QueryInspection.sql' />

	<sql alias='查找安检单带维修' path='QueryCheckPaperWithRepair.sql' />
	<!-- 考勤 -->
	<sql alias='考勤查询' path='QueryAttendance.sql'/>
	<!-- 得到所长管辖的员工 -->
	<sql alias='得到所长管辖的员工' path='FetchAllEmpUnderABranch.sql' />
	<!-- 提取考勤信息 -->
	<sql alias='提取考勤信息' path='FetchAttendance.sql' />
	<!-- 获取最新手机状态 -->
	<sql alias='获取最新手机状态' path='FetchLatestPhoneState.sql' />
	<!-- 获取足迹-->
	<sql alias='获取足迹' path='FetchFootprint.sql' />
	<!-- 统计安检员年任务-->
	<sql alias='统计安检员年任务' path='AggCheckerAnnualPlan.sql' />
	<!-- 统计安检员年任务明细-->
	<sql alias='统计安检员年任务明细' path='AggCheckerAnnualPlanDetail.sql' />
	<!-- 统计安检员月任务-->
	<sql alias='统计安检员月任务' path='AggCheckerMonthlyPlan.sql' />
	<!-- 统计维修员年任务-->
	<sql alias='统计维修员年任务' path='AggPlumberAnnualPlan.sql' />
	<!-- 统计维修员年任务明细-->
	<sql alias='统计维修员年任务明细' path='AggPlumberAnnualPlanDetail.sql' />
	<!-- 统计维修员月任务-->
	<sql alias='统计维修员月任务' path='AggPlumberMonthlyPlan.sql' />
	<!-- 最后在线位置及状态 -->
	<sql alias='最后在线位置及状态' path='FetchLastKnownState.sql' />
	<!-- 统计月度流量 -->
	<sql alias='月度流量统计' path='AggMonthTraffic.sql' />
	<!-- 日流量明细 -->
	<sql alias='日流量明细' path='FetchDailyTrafficList.sql' />
	<!-- 跟踪安检员年任务-->
	<sql alias='跟踪安检员年任务' path='AggCheckerAnnualPlanPaged.sql' />
	<!-- 跟踪安检员年任务明细-->
	<sql alias='跟踪安检员年任务明细' path='AggCheckerAnnualPlanDetailPaged.sql' />
	<!-- 跟踪安检员月任务-->
	<sql alias='跟踪安检员月任务' path='AggCheckerMonthlyPlanPaged.sql' />
	<!-- 跟踪维修员年任务-->
	<sql alias='跟踪维修员年任务' path='AggPlumberAnnualPlanPaged.sql' />
	<!-- 跟踪维修员年任务明细-->
	<sql alias='跟踪维修员年任务明细' path='AggPlumberAnnualPlanDetailPaged.sql' />
	<!-- 跟踪维修员年任务明细-->
	<sql alias='跟踪维修员月任务' path='AggPlumberMonthlyPlanPaged.sql' />
	<!-- 用ID删除通知 -->
	<sql alias='用ID删除通知' path='DeleteNoticeById.sql' />
	<!-- 燃气表-->
	<sql alias='燃气表使用情况' path='GasMeterUsage.sql'/>
	<sql alias='按年月人员提取安检计划' path='FetchCheckerPlan.sql'/>
	<sql alias='隐患明细查询' path='QueryDefectsDetail.sql'/>
	<sql alias='本期用气量查询' path='GasUsageThisTerm.sql'/>
	<sql alias='计划项是否已经审核' path='FetchVerifiedPlanItem.sql' />
	<sql alias='提取用户表读数' path='FindUserMeterReadingById.sql'/>
	<sql alias='查找已经存在的安检单' path='FindExistingCheckPaper.sql'/>
	<sql alias='查找安检表记录' path='FindMeterChecks.sql'/>
	<sql alias='获取计划项审核状态' path='FetchPlanItemApprovalState.sql' />
	<sql alias='获取维修审核状态' path='FetchRepairApprovalState.sql' />


	<sql alias='用户表档案' path='meterfiles.sql'/>
	<!-- 根据表号查询信息 -->
	<sql alias='getmeternum' path='getmeternum.sql'/>
	<!-- 查询用户信息 -->
	<sql alias='getuser' path='getuser.sql'/>
	<sql alias='提取表档案' path='FetchMetersByUserId.sql'/>
	<sql alias='用用户号提取表档案' path='FetchMetersByUserNo.sql'/>
	<sql alias='提取临时表档案' path='FetchTmpMetersById.sql'/>
	<sql alias='查找计划项最后安检日期' path='FetchLastDateByItemId.sql'/>
	<sql alias='根据计划项查找用户' path='FetchUserByPlanItem.sql'/>
	<sql alias='根据计划项找表' path='FetchMeterByPlanItem.sql'/>
	<!-- 根据id查询表信息 -->
	<sql alias='FindUserId' path='FindUserId.sql' />
	<sql alias='提取用户档案' path='FetchUserByChecker.sql' />
	<sql alias='findNeedRecheck' path='findNeedRecheck.sql' />
	<!--<sql alias='FetchUserByChecker' path='FetchUserByChecker.sql' />-->
	<!-- 查询隐患 -->
	<sql alias='getDefect' path='getDefect.sql' />
	<sql alias='查询计划项表信息' path='FetchPlanUserfiles.sql' />
	<sql alias='查询计划项设备信息' path='FetchPlandevices.sql' />
	<sql alias='查询用户原信息' path='FetchNomodifyUserinfo.sql' />
	<sql alias='查询用户旧信息' path='FetchNomodifyHistoryUserinfo.sql' />
	<sql alias='查询用户表原信息' path='FetchNomodifyMeterinfo.sql' />
	<sql alias='CheckPaperSearchItem' path='CheckPaperSearchItem.sql' />
	<sql alias='QueryCheckRate' path='QueryCheckRate.sql' />
	<!--已不用<sql alias='QueryCheckerRate' path='QueryCheckerRate.sql' />
	<sql alias='QueryMonthCheckRate' path='QueryMonthCheckRate.sql' />-->
	<sql alias='login' path='login.sql' />
	<sql alias='safeCheckgetUserInfo' path='getUserInfo.sql' />
	<sql alias='getShareCheckPlan' path='getShareCheckPlan.sql' />
	<sql alias='getDeviceItem' path='getDeviceItem.sql' />
	<sql alias='SafeCheckFetchFootprint' path='SafeCheckFetchFootprint.sql' />
	<sql alias='SafeCheckGetAllCurrentPosition' path='SafeCheckGetAllCurrentPosition.sql' />

	<!--获取该次下发中已经存在的项目-->
	<sql alias='getExistCheckItem' path='getExistCheckItem.sql' />
	<!--获取该安检单的状态-->
	<sql alias='GetCheckItemState' path='GetCheckItemState.sql' />
	<!--根据隐患获取安检单-->
	<sql alias='GetCheckPaperByDefect' path='GetCheckPaperByDefect.sql' />
	<!--根据安检单id获取隐患详情-->
	<sql alias='GetDefectByPaperId' path='GetDefectByPaperId.sql' />
	<!--获取转维修需要的用户信息-->
	<sql alias='GetUserForRepaire' path='GetUserForRepaire.sql' />
	<!--安检单表排序查询-->
	<sql alias='safe_singleTable_OrderBy' path='singleTable_OrderBy.sql' mobile="true"/>
	<!--安检单表分类查询-->
	<sql alias='safe_singleTable_GroupBy' path='safe_singleTable_GroupBy.sql' mobile="true" />
	<!--安检活动查询-->
	<sql alias='getCheckSend' path='getCheckSend.sql' />
	<!--获取安检计划名称-->
	<sql alias='getCheckplan' path='getCheckPlan.sql' />
	<!--获取所有小区-->
	<sql alias='getallarea' path='getallarea.sql' />
	<!--临时存放安检情况汇总-->
	<sql alias="getCheckPlanAreaList" path="getCheckPlanAreaList.sql"/>
	<sql alias="getCheckPlanUserList" path="getCheckPlanUserList.sql"/>
	<!--手机端在线查询安检员工作量-->
	<sql alias='AndroidCheckInfo' path='AndroidCheckInfo.sql' />
	<!--获取所有隐患级别-->
	<sql alias="GetAllDefectLevel" path="GetAllDefectLevel.sql"/>
	<!--获取隐患项展示隐患级别-->
	<sql alias="GetDefectPaperWithDefectLevel" path="GetDefectPaperWithDefectLevel.sql"/>
	<!-- 隐患报表 -->
	<sql alias='HiddenSituation' path='HiddenSituation.sql' />
	<!-- 隐患类别 -->
	<sql alias='getDefectType' path='getDefectType.sql' />
	<sql alias='查询安检项' path='FetchPlanItem.sql'/>

	<sql alias='getRemind' path='getRemind.sql' />
	<sql alias="yinhuanCount" path="yinhuanCount.sql"/>
	<sql alias="SafeHiddenDisposal" path="SafeHiddenDisposal.sql"/>

	<sql alias="defectCount" path="defectCount.sql"/>

	<!--	安检员考核-->
	<sql alias="checkerCount" path="checkerCount.sql"/>
	<!-- 开关阀查询用户信息-->
	<sql alias="SwitchValveSeachUser" path="SwitchValveSeachUser.sql"/>
	<!-- 安检周期查询-->
	<sql alias="safe_getcirclelist" path="safe_getcirclelist.sql"/>
	<!-- 公告获取-->
	<sql alias="FetchAnnouncement" path="FetchAnnouncement.sql"/>
	<sql alias="PcDealWith" path="PcDealWith.sql"/>
	<sql alias="AndroidDealWith" path="AndroidDealWith.sql"/>
	<!--在线查询安检记录-->
	<sql alias='Get_khrq' path='Get_khrq.sql'/>
	<sql alias='Get_khrq_count' path='Get_khrq_count.sql'/>

	<sql alias='defectDetails' path='DefectDetails.sql'/>

	<sql alias='zhoukouCheckCount' path='zhoukouCheckCount.sql'/>

	<!--澧县安检汇总-->
	<sql alias="LXgetCheckPlanAreaList" path="LXgetCheckPlanAreaList.sql"/>

	<!--获取位置信息-->
	<sql alias="queryPhoneLocationToUser" path="mapLocation/queryPhoneLocationToUser.sql"/>
	<!--根据开始时间和结束时间获取用户轨迹信息 更新定位服务单独放开-->
	<!--<sql alias="QueryUserTracksByCompanySql" path="mapLocation/QueryUserTracksByCompanySql.sql"/>-->
	<!--总公司汇总显示分公司安检情况-->
	<sql alias="FilialeCheckCount" path="FilialeCheckCount.sql"/>

	<sql alias="getUserByCheckBook" path="getUserByCheckBook.sql"/>
	<sql alias="getUserByCheckBookCompany" path="getUserByCheckBookCompany.sql"/>

	<sql alias="getChazhi" path="getChazhi.sql"/>
	<sql alias="QueryCheckBook" path="QueryCheckBook.sql"/>
	<sql alias='SafecheckTableModel' path='SafecheckTableModel.sql' />
	<!--无计划安检汇总-->
	<sql alias='SafecheckSummary' path='SafecheckSummary.sql' />
	<sql alias='getUserByCheckBookuser' path='getUserByCheckBookuser.sql' />


	<!--新隐患查看-->
	<sql alias='GetCheckPaperByDefectNew' path='defect/GetCheckPaperByDefectNew.sql' />
	<sql alias='GetDefectByPaperIdNew' path='defect/GetDefectByPaperIdNew.sql' />
	<sql alias='GetUserForRepaireNew' path='defect/GetUserForRepaireNew.sql' />

	<!--新隐患查看2-->
	<sql alias='GetCheckPaperByDefectNew2' path='DefectDealMain/GetCheckPaperByDefectNew.sql' />
	<sql alias='GetDefectByPaperIdNew2' path='DefectDealMain/GetDefectByPaperIdNew.sql' />
	<sql alias='GetUserForRepaireNew2' path='DefectDealMain/GetUserForRepaireNew.sql' />


	<!--获取安检状态统计数据接口-->
	<sql alias="GetSecurityCheckStatisticss" path="SecurityCheckInterface/GetSecurityCheckStatisticss.sql"/>
	<!--获取安检类型数据接口-->
	<sql alias="GetSecurityCheckTypedatas" path="SecurityCheckInterface/GetSecurityCheckTypedatas.sql"/>
	<!--获取安检总户数接口-->
	<sql alias="GetTotalNumberCheckpoints" path="SecurityCheckInterface/GetTotalNumberCheckpoints.sql"/>
	<!--获取安检隐患分布情况接口-->
	<sql alias="ObtainDistributionSecurityRisks" path="SecurityCheckInterface/ObtainDistributionSecurityRisks.sql"/>
	<!--获取安检员任务情况数据接口未完成前五-->
	<sql alias="ObtainTaskStatusDataSecurityInspectors" path="SecurityCheckInterface/ObtainTaskStatusDataSecurityInspectors.sql"/>
	<!--获取安检员任务情况数据接口完成前五-->
	<sql alias="ObtainChecktaskSituationUnfinisheds" path="SecurityCheckInterface/ObtainChecktaskSituationUnfinisheds.sql"/>
	<!--第三方获取安检PC人员账号密码 （同步账号密码）-->
	<sql alias='DSFGetPcChecker' path='DSFGetPcChecker.sql' />

	<!--安检册查询列表-->
	<sql alias='getCheckBookList' path='checkbook/getCheckBookList.sql' />
	<sql alias='getCheckBookArea' path='checkbook/getCheckBookArea.sql' />
	<sql alias='getCheckBookCompany' path='checkbook/getCheckBookCompany.sql' />

	<!-- 手机端安检计划列表 -->
	<sql alias="androidCheckPlanList" path="AndroidCheckPlanList.sql" mobile="true" />
	<sql alias='SafeUserChecker' path='SafeUserChecker.sql' />

	<!--隐患整改报表-->
	<sql alias='getSafeCheckerName' path='report/getCheckerName.sql' />
	<sql alias='getSafeDefectLevel' path='report/getDefectLevel.sql' />
	<sql alias='getSafeDeviceType' path='report/getDeviceType.sql' />
	<sql alias='getSafeItemName' path='report/getItemName.sql' />
	<sql alias='checkByPlanData' path='report/checkByPlanData.sql' />
	<sql alias='getAllPlanDate' path='report/getAllPlanDate.sql' />
	<sql alias='checkPlanAreaReportData' path='report/checkPlanAreaReportData.sql' />
	<!--无计划汇总-->
	<sql alias='noplanCount' path='defect/NoPlanCount.sql'/>
	<sql alias='noplanItem' path='defect/NoPlanItem.sql'/>
	<!--安检计划统计图表-->
	<sql alias="CheckPlanCountChart" path="report/CheckPlanCountChart.sql"/>
	<!--安检考核，根据小区+安检员分类-->
	<sql alias="CheckerExamineByArea" path="report/CheckerExamineByArea.sql"/>
	<sql path="report/searchGroupByArea.sql" alias="searchGroupByArea"/>
	<sql alias="CheckPlanCountChartArea" path="report/CheckerExamineByArea.sql"/>
	<!-- 获取手机状态 -->
	<sql alias="getPhoneState" path="mapLocation/getPhoneState.sql"/>
	<!-- 获取手机状态 -->
	<sql alias="QueryCheckerPoint" path="mapLocation/QueryCheckerPoint.sql"/>
	<!-- 根据用户id获取用户自行整改的隐患 -->
	<sql alias="GetDefectByUserinfoId" path="defect/GetDefectByUserinfoId.sql"/>
	<sql alias="wechat_GetDefectByUserinfoId" path="defect/GetDefectByUserinfoId.sql"/>
	<!-- 微信审核综合查询 -->
	<sql alias="getSafecheckResult" path="defect/getSafecheckResult.sql"/>
	<sql alias="wechat_getSafecheckResult" path="defect/getSafecheckResult.sql"/>
	<!-- 查询所有的用户拉黑状态 -->
	<sql alias="getUserinfoBaskListState" path="getUserinfoBaskListState.sql"/>
	<!-- 安检预约中心查询预约表数据 -->
	<sql alias="queryOrderCenterSafe" path="queryOrderCenter.sql"/>
	<!-- 甘泉安检员考核 -->
	<sql alias="performanceStatistics" path="performanceStatistics.sql"/>
	<!-- 安检旧数据查询 -->
	<sql alias="getOldCheckPaper" path="getOldCheckPaper.sql"/>
	<!-- 铜川隐患闭环报表 -->
	<sql alias="yinHuanBiHuanReport1" path="report/yinHuanBiHuanReport1.sql"/>
	<sql alias="yinHuanBiHuanReport2" path="report/yinHuanBiHuanReport2.sql"/>
	<sql alias="yinHuanBiHuanReport3" path="report/yinHuanBiHuanReport3.sql"/>
	<!-- 到访不遇查询 -->
	<sql alias="queryNoVisit" path="queryNoVisit.sql"/>
	<!-- 榆川隐患明细（迁移到产品） -->
	<sql alias="DefectDetailsYC" path="DefectDetailsYC.sql"/>
	<!--安检统计-->
	<sql alias="CountPeople" path="CountPeople.sql" />
	<sql alias="YearCountRate" path="YearCountRate.sql" />
	<sql alias="YearCheckerCountRate" path="YearCheckerCountRate.sql" />
	<sql alias="CheckComplete" path="CheckComplete.sql" />
	<sql alias="CheckStatusQuery" path="CheckStatusQuery.sql" />
	<sql alias="HiddenLevel" path="HiddenLevel.sql" />
	<sql alias="QueryCheckStatus" path="QueryCheckStatus.sql" />
	<sql alias="QueryHiddenLevel" path="QueryHiddenLevel.sql" />
	<!-- 预约安检在线查询 -->
	<sql alias='YuYueFetchUserByChecker' path='YuYueFetchUserByChecker.sql' />
	<sql alias='GetSafeHistorycount' path='GetSafeHistorycount.sql' />
	<sql alias='GetSafetHistory' path='GetSafetHistory.sql' />

	<sql alias='CheckerReportSql1' path='report/CheckerReportSql1.sql' />
	<sql alias='CheckerReportSql2' path='report/CheckerReportSql2.sql' />
	<sql alias='safeStatisticsYearReportSql2' path='report/safeStatisticsYearReportSql2.sql' />
	<sql alias='safeStatisticsYearReportSql3' path='report/safeStatisticsYearReportSql3.sql' />
	<sql alias='safeDetail' path='SafeDetail.sql' />
	<sql alias='areaPitfallReportSql1' path='report/AreaPitfallReportSql1.sql' />
	<sql alias='areaPitfallReportSql2' path='report/AreaPitfallReportSql2.sql' />
	<sql alias='communityDetail' path='CommunityDetail.sql' />
	<sql alias='communityTypeDetailG' path='CommunityTypeDetailG.sql' />
	<sql alias='CommunityTypeDetailM' path='CommunityTypeDetailM.sql' />
	<sql alias='safeStatisticsDay' path='SafeStatisticsDay.sql' />
	<sql alias='safeStatisticsMonth' path='SafeStatisticsMonth.sql' />
	<sql alias='safeStatistics' path='SafeStatistics.sql' />
</cfg>
