log.info("uploadPaper:------进入设备信息上传过程-----"),

log.info( "进入设备信息上传: {data}"),
data = data.row,
// 记录上传是否成功
f_success = true,

// 上传图片
		url = "{context.cc_base_url}/api/af-safecheck",
		resolve = UploadPic.uploadPic(url,data),

		resolve == null: (
			log.info( "上传图片失败"),
			f_success = false
		), (
			log.info( "上传图片成功")
		),


log.info( "上传图片结束"),

// 上传工单
restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/updateDevice", data:resolve.toString()},

log.info("开始上传工单：{data.toString()}"),
params = restTools.action(restJson),
log.info("上传结果：{params.code}"),

params
