log.info("ServiceTimeOut{context.f_repairman_id}"),
log.info("ServiceTimeOut{context.cc_base_url}"),

//获取本地安检计划及安检计划项
plans = sql.action({alias:"GetNewPlan",criteria:{f_checker : context.f_repairman_id}}),

log.info("本地安检计划及安检计划项：{plans}"),
// 安检定时提取标记，默认1，代表本地无数据即第一次安装或者清除数据后全部下载
log.info("plans.data.rows.length()：{plans.data.rows.length()}"),
 plans.data.rows.length() > 0:
(
    plans.data.rows.each(
        list = sql.action({sql: "select id,version from t_check_plan_item where f_plan_id= '{row.id}' and (f_upload_state = '未传' or (f_last_check_state != '入户' and f_upload_state = '已传'))"}),
        row.put("f_items",list.data)
    )
    // 安检定时提取标记，当本地存在工单代表非第一次下载，赋值为2
),
null,

// 提取数据, body是参数，cc_base_url是提取路径
body = {
	data: {
		f_checker: context.f_repairman_id,
		services: plans.data
	}
},
log.info("body"),
// 获得工单变化情况
changed = restTools.action({
    action: "post",
    url: "{context.cc_base_url}/api/af-safecheck/logic/getCheckPlanChanged",
    data: body.toString()
   }),

params = {code:200,hasNewMsg:0},
changed.code==200 : (
    // 保存所有新增工单
    inserts = changed.data.result.inserts,
    log.info("inserts.length====>{inserts.length()}"),
    inserts.each(
        log.info("{row.f_plan_name}====>{row.f_items.length()}"),
    	entity.action({
            method: "save",
            entity: "t_check_plan",
            data: row
        }),
    	params.hasNewMsg = 1
    ),

    // 删除所有要删除的工单
    deletes = changed.data.result.deletes,
     log.info("deletes.length====>{deletes.length()}"),
    deletes.each(
    	sql.action({sql: "
                    delete from t_check_plan_item where f_plan_id ='{row.id}' and (f_upload_state = '未传' or (f_last_check_state != '入户' and f_upload_state = '已传'))
                ", cmd: "cmd"}),
    	entity.delete({entity:"t_check_plan",id:"{row.id}"})
    ),

        In = changed.data.result.itemIn,
         log.info("In.length====>{In.length()}"),
        In.each(
        // 保存工单变化
            	entity.action({
                                method: "save",
                                entity: "t_check_plan_item",
                                data: row
                            }),
            	params.hasNewMsg = 1
        ),




         De = changed.data.result.itemDe,
          log.info("De.length====>{De.length()}"),
        De.each(
            sql.action({sql: "delete from t_check_plan_item where id = '{row.id}'", cmd2: "cmd2"})
        ),



        Mo = changed.data.result.itemMo,
         log.info("Mo.length====>{Mo.length()}"),
        Mo.each(
                sql.action({sql: "
                                delete from t_check_plan_item where id = '{row.id}'
                                ", cmd2: "cmd2"}),

    	        // 保存工单变化
    	        entity.action({
                                   method: "save",
                                   entity: "t_check_plan_item",
                                   data: row
                            }),
    	    params.hasNewMsg = 1
        )

),null,
params
