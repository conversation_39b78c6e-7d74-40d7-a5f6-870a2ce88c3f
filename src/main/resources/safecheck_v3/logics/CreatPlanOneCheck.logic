log.info("------进入第三方调用建工单服务-----"),
res="",
    //  先判断该安检员本月是否有专项安检计划
    f_plan_name = "{dateTools.getNowYear()}年{dateTools.getNowMonth()}月{data.Checker}专项安检计划",
    planarr = sql.querySQL("v3_safecheck_sql", "SELECT id  from t_check_plan where f_plan_name = '{f_plan_name}' and f_checker_id = '{data.CheckerId}'"),
    planflag = "不存在",
    itemflag = "不存在",
    planarr.length() > 0: (
        planid = planarr[0].id,
        planflag = "已存在",
        item = sql.querySQL("v3_safecheck_sql", "SELECT id  from t_check_plan_item where f_plan_id = '{planid}' and f_userinfoid = '{data.UserInfoId}'"),
        item.length() > 0: (
            itemflag = "已存在"
        ),null
    ),(
        planid = commonTools.getUUID()
    ),
    planflag == "不存在" : (
      plan={
             id:planid,
             f_plan_name:f_plan_name,
             f_plan_year:dateTools.getNowYear(),
             f_plan_month:dateTools.getNowMonth(),
             f_plan_type:"预约计划",
             f_order_source:"{data.f_order_source}",
             f_issued:"否",
             f_filialeid:"{data.Filialeid}",
             f_create_operator:"{data.CreateOperator}",
             f_create_time: dateTools.getNow2(),
             f_safecheck_type:"专项安检",
             f_subcompany:"{data.Subcompany}",
             f_prearranged_date:"{data.f_orderdate}",
             f_checker:"{data.Checker}",
             f_checker_id:"{data.CheckerId}"
         },
         aa=logic.run("OutCreateCheckPlan",plan),
           f_subscribe_date = "",
             data={
                 f_safecheck_type:"专项安检",
                 f_subscribe_date:"{f_subscribe_date}",
                 f_plan_id:"{planid}",
                 f_operator:"{data.CreateOperator}",
                 f_filialeid:"{data.Filialeid}",
                 f_prearranged_date:"{data.f_orderdate}",
                 condition:"ui.f_userinfo_id='{data.UserInfoId}'",
                 switchCheckAll:false,
                 checkAll:false
                 },
          logic.run("OutAddCheckPlanItem",data),
          res={code:200,result:"提交成功",info:"正常"}
    ),(
       itemflag == "不存在" : (
       f_subscribe_date = "",
                 data={
                     f_safecheck_type:"专项安检",
                     f_subscribe_date:"{f_subscribe_date}",
                     f_plan_id:"{planid}",
                     f_operator:"{data.CreateOperator}",
                     f_filialeid:"{data.Filialeid}",
                     f_prearranged_date:"{data.f_orderdate}",
                     condition:"ui.f_userinfo_id='{data.UserInfoId}'",
                     switchCheckAll:false,
                     checkAll:false
                     },
         logic.run("OutAddCheckPlanItem",data),
         res={code:200,result:"提交成功",info:"正常"}
       ),(
         res={code:200,result:"提交成功",info:"异常：该用户已存在安检计划中"}
       )
    ),
res
