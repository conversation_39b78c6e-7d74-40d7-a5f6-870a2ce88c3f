log.debug("saveSealBind-data: {data}"),
seals = sql.querySQL("seal_sql", "select * from t_seals_record where f_uid = '{data.seal.f_uid}'"),
log.debug("seals: {seals}"),
seals.length() > 0 : (
    seal = seals[0]
), (
    seal = {}
),
seal.f_userinfo_id != null : (
    result = {code: 712, msg: "该铅封已被其它用户绑定！"}
),(
    oldSeals = sql.querySQL("seal_sql", "select * from t_seals_record where f_userinfo_id = '{data.seal.f_userinfo_id}' and f_state = '有效'"),
    oldSeals.length() > 0 : (
        sql.execSQL("seal_sql","
            update t_seals_record
            set f_unsea_operate_name = '{data.seal.f_operate_name}', f_unsea_operate_id = '{data.seal.f_operate_id}',
                f_unsea_operate_date = '{data.seal.f_operate_date}', f_state = '无效'
            where f_userinfo_id = '{data.seal.f_userinfo_id}'
        ")
    ), null,
    seals.length() == 0 : (
        data.seal.f_devices_no = data.f_devices_no,
        data.seal.f_creation_date = data.seal.f_operate_date,
        data.seal.f_creation_name = data.seal.f_operate_name,
        data.seal.f_creation_name_id = data.seal.f_operate_id,
        log.debug("data.seal: {data.seal}"),
        saveRes = entity.partialSave("t_seals_record", data.seal),
        sealId = jsonTools.convertToJson(saveRes).id
    ), (
        sealId = seals[0].id
    ),
    log.debug("sealId: {sealId}"),
    data.operateList.f_seare_id = sealId,
    saveRes = entity.partialSave("t_operate_list", data.operateList),
    operateListId = jsonTools.convertToJson(saveRes).id,
    log.debug("operateListId: {operateListId}"),
    data.seal.id = sealId,
    data.seal.f_operate_list_id = operateListId,
    entity.partialSave("t_seals_record", data.seal),
    data.files.each(
        row.f_operate_list_id = operateListId,
        log.debug("row: {row}"),
        entity.partialSave("t_lead_operate_files", row)
    ),
    result = {
        code: 200,
        msg: "绑定成功！"
    }
),
result
