// 向计划内添加用户
// 先检索目标计划中已有的用户，已存在的用户不予增加
updateitem = 0,
existitem = 0,
oldarrs = sql.querySQL("v3_safecheck_sql", "select * from t_check_plan_item where f_plan_id = '{data.planid}'"),
array = data.rows,
array.each(
      itemrow = row,
      sign = "yes",
      oldarrs.each(
          row.f_userinfoid == itemrow.f_userinfo_id : (
                sign = "no"
                 ),null
               ),
       sign == "yes" : (
         sql.execSQL("v3_safecheck_sql", "update t_userinfo set f_issued_time = CONVERT(varchar,GETDATE(),120) where f_userinfo_id = '{itemrow.f_userinfo_id}'"),
         meters = sql.query("查询计划项表信息",{f_userid:itemrow.f_userinfo_id}),
         	entity.partialSave(
         		"t_check_plan_item",
         		{
         			id:commonTools.getUUID(),//生成计划项id
         			f_userinfo_code: itemrow.f_userinfo_code,//用户档案id
         			f_userinfoid: itemrow.f_userinfo_id,//用户档案id
         			f_slice_area: itemrow.f_slice_area, //片区
         			f_street: itemrow.f_street,//街道
         			f_residential_area: itemrow.f_residential_area,//小区
         			f_building: itemrow.f_building,//楼号
         			f_unit: itemrow.f_unit,//单元
         			f_floor: itemrow.f_floor,//楼层
         			f_room: itemrow.f_room,//门牌号
         			f_address: itemrow.f_address,//详细地址
         			f_state: "未检",//状态
         			f_upload_state: "未传",//上传状态
         			f_plan_id:data.planid,
         			f_no_checkplan:"有计划安检",
         			f_user_name: itemrow.f_user_name,
         			f_user_phone: itemrow.f_user_phone,
         			f_user_type: itemrow.f_user_type,
//         			f_subcompany:itemrow.f_filiale,
//         			f_branch:itemrow.f_filiale,
         			f_approved: "未审核",
         			f_no_checkplan:"有计划安检",
         			f_last_check_state: itemrow.f_last_check_state,
         			f_last_check_date: itemrow.f_last_check_date,
         			f_repair_approved: "未审核",
//         			f_orgstr:data.f_filialeid,
                    f_filialeid:data.f_filialeid,
                    f_plan_meters:meters,
         			version:0,
         			f_last_check_result:itemrow.f_last_check_result,
                    f_comments:itemrow.f_comments
         		}
         	),
          updateitem = updateitem + 1
        ),(
           existitem =  existitem + 1
        )
),
result = {
    updateitem : updateitem,
    existitem : existitem
},
result
