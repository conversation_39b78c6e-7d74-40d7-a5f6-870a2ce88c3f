res = "",
try {
    data.Filialeid = "305",
    subscribe_date = "{data.OverDate}",
    hour = subString.sub("{subscribe_date}",11,13),
    dateget = subString.sub("{subscribe_date}",0,10),
    f_subscribe_date = "",
    hour > 11 :(
        f_subscribe_date = "{dateget} 23:59:59"
    ),(
        f_subscribe_date = "{dateget} 12:00:00"
    ),
    plan = {
        f_plan_name: "{dateTools.getNowYear()}年{dateTools.getNowMonth()}月{dateTools.getNowDay()}日{data.UserName}{data.SafeType}{data.UserType}计划",
        f_plan_year: dateTools.getNowYear(),
        f_plan_month: dateTools.getNowMonth(),
        f_plan_type: "预约计划",
        f_issued: "否",
        f_filialeid: "{data.Filialeid}",
        f_create_operator: "{data.CreateOperator}",
        f_create_time: dateTools.getNow2(),
        f_safecheck_type: "{data.SafeType}",
        f_checker: "{data.Checker}",
        f_checker_id: "{data.CheckerId}"
    },
    aa=logic.run("createCheckPlan",plan),
    data = {
        f_safecheck_type: "{data.SafeType}",
        f_subscribe_date: "{f_subscribe_date}",
        f_plan_id: "{aa.id}",
        f_operator: "{data.CreateOperator}",
        f_filialeid: "{data.Filialeid}",
        condition: "ui.f_userinfo_id='{data.UserInfoId}'",
        switchCheckAll: false,
        checkAll: false
    },
    log.run("AddCheckPlanItem",data),
    res = {
        code: 200,
        result: "创建成功"
    }
} catch(Exception e) {
    res = {
        code: 500,
        result: "创建失败"
    }
},
res
