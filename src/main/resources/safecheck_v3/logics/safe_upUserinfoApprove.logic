
// 保存任务分发过程
res={code:500,msg:"提交审核信息失败！"},
log.info("safe_upUserinfoApprove------------data is {data}-----------"),

data.row.changemeterno == true : (
    imgfileName = (data.row.f_meterno_path).substring((data.row.f_meterno_path).lastIndexOf("/") + 1, (data.row.f_meterno_path).lastIndexOf("?")),
	log.info("上传图片mc-----------{imgfileName}"),
    url = "{context.cc_base_url}/api/af-safecheck/file/savefile?module=telephone&filename={imgfileName}",
    log.info("upload------------url is {url}-----------"),
    file = data.row.f_meterno_path.substring(8,(data.row.f_meterno_path).lastIndexOf("?")),
    log.info("upload------------file is {file}-----------"),
    imgid = UploadPlugin.upload(url,file),
    imgid == null: (
        log.info("upload上传图片失败"),
        errMsg = "{errMsg} 上传{row.imgpath}图片失败!",
        res={code:500,msg:"上传图片失败,请检查照片是否存在,如无问题请再次提交！"},
        f_success = false
    ),(
        log.info("upload上传图片成功"),
        data.row.f_meterno_path=imgfileName,
        restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/SaveEntity", data:data},
        params = restTools.action(restJson),
        params.code==200:(
            res={code:200}
        ),(
            res={code:500,msg:"提交审核信息失败"}
        )
    )
),(
    restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/SaveEntity", data:data},
    params = restTools.action(restJson),
    params.code==200:(
        res={code:200}
    ),(
        res={code:500,msg:"提交审核信息失败"}
    )
),
res

