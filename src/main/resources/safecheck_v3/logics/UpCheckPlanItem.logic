//根据参数做对安检计划项的增删改
//data:{
//      {                                                                                   }
//      f_plan_id:'数据',
//      f_operator:'',
//      condition:'sql条件', 查询条件
//      switchCheckAll:true/false是否有选择功能
//      checkAll:true/false, 是否选中 ‘全选按钮’
//      param[...], 选中的字段的值
//      columnName 需要做查询的字段名
//}
//
sqlstr = logic.run("ConditionalGeneration",data),
log.info("执行修改====data:{data}"),
data.f_create_time=dateTools.getNow2(),
newid=commonTools.getUUID(),
//将对应的地址表中f_plan_id也进行修改
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_plan_id='{data.f_plan_id}'
            where f_userinfo_id in (
              select ua.f_userinfo_id from t_user_address ua
              join t_check_plan_item i on ua.f_userinfo_id=i.f_userinfoid
              where {sqlstr} and i.f_state = '未检' group by ua.f_userinfo_id
          )
"),
//修改计划项对应的计划id
sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set f_plan_id = '{data.f_plan_id}',version = version + 1
    where id in (
        select id from t_check_plan_item i where {sqlstr}
    )
 "),

{code:200}
