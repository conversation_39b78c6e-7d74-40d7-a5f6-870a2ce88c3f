// 查找安检计划项对应的最后安检日期


log.info("-------------进入安检档案回写{data}-------------------"),

sql.execSQL("v3_safecheck_sql", "update t_userinfo set f_last_check_date = '{data.item.f_offsite_time}',f_last_check_state = '{data.item.f_entry_status}'  where f_userinfo_id = '{data.item.f_userinfoid}'"),

data.item.f_entry_status=="到访不遇":(
param=sql.querySQL("v3_safecheck_sql", "select f_check_version from t_user_address where f_userinfo_id = '{data.item.f_userinfoid}'"),
param[0].f_check_version==null:(
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version =1 where f_userinfo_id = '{data.item.f_userinfoid}'"),
sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set f_check_version =1 where id = '{data.item.f_check_item_id}'")


),null,
param[0].f_check_version=="1":(
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version =f_check_version+1 where f_userinfo_id = '{data.item.f_userinfoid}'"),
sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set f_check_version =f_check_version+1 where id = '{data.item.f_check_item_id}'")

),null,
param[0].f_check_version=="2":(
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version =null where f_userinfo_id = '{data.item.f_userinfoid}'"),
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_plan_id=null, f_last_check_date = '{data.item.f_offsite_time}', f_last_check_state = '{data.item.f_entry_status}', f_last_check_result='{data.item.f_defect_content}',f_last_checker='{data.item.f_checker_name}' where f_userinfo_id = '{data.item.f_userinfoid}'"),
sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set f_check_version =f_check_version+1 where id = '{data.item.f_check_item_id}'")

),null

),(
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version =null,f_plan_id=null, f_last_check_date = '{data.item.f_offsite_time}', f_last_check_state = '{data.item.f_entry_status}', f_last_check_result='{data.item.f_defect_content}',f_last_checker='{data.item.f_checker_name}' where f_userinfo_id = '{data.item.f_userinfoid}'"),
sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set f_check_version =null where id = '{data.item.f_check_item_id}'")

),
{code:200}
