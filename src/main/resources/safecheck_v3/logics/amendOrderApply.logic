log.info("自行整改审核 >>> {data}"),
repairdate=dateTools.getNow2(),
//  修改预约信息
sql.execSQL("v3_safecheck_sql", "update t_order_center set f_suggest='{data.f_suggest}',f_orderstate = '{data.f_orderstate}',f_dispose_operator='{data.f_dispose_operator}',f_disposedate='{data.f_disposedate}' where id = '{data.id}'"),
data.f_orderstate=="预约成功" :(
    sql.execSQL("v3_safecheck_sql", "update t_devices_items set f_repairman='{data.defect.f_repairman}',f_is_repaired='{data.defect.f_is_repaired}',
        f_repair_path='{data.defect.f_repair_path}',f_repair1_path='{data.defect.f_repair1_path}',
        f_repair_date=format(getdate(),'yyyy-MM-dd HH:mm:ss'),f_repair_path_source = '{data.defect.f_repair_path_source}'
        where id = '{data.defect.id}'"),
    papers = sql.querySQL("v3_safecheck_sql", "
        select di.id,di.f_item_name
        from t_paper_devices pd
        left join t_devices_items di on pd.id=di.f_device_id
        left join t_devices_items_lists dil on dil.f_item_id=di.id
        where pd.f_paper_id='{data.defect.f_paper_id}'
        and di.f_is_defect = 'true' and (di.f_deal_dispose in ('自行整改', '自行处理', '报修') and (di.f_is_repaired != '已处理' or di.f_is_repaired is null))
    "),
    papers.length() == 0 :(
        sql.execSQL("v3_safecheck_sql", "
            update t_check_paper set f_repaired='已修',f_repairman='{data.defect.f_repairman}', f_repair_date = '{repairdate}' where id = '{data.defect.f_paper_id}'
        "),
        //  解除限购
        res = sql.querySQL("v3_safecheck_sql", "SELECT value FROM t_singlevalue WHERE name IN ('工单完成时是否解除限购')"),
        res != [] :(
           res[0].value == "是" :(
               res = logic.run("CancelLimited",{f_userinfo_code: data.defect.f_userinfo_code}),
               log.info("调用解除限购接口返回 >>> {res}")
           ),null
        ),null
    ),null
),null,
{
    status: 200
}
