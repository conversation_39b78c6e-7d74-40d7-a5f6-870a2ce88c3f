//新增安检计划项 并添加活动
//data:{
//      {                                                                                   }
//      f_plan_id:'',
//      f_operator:'',
//       f_filialeid:'',
//      f_create_time:'',
//      condition:'sql条件', 查询条件
//      switchCheckAll:true/false是否有选择功能
//      checkAll:true/false, 是否选中 ‘全选按钮’
//      param[...], 选中的字段的值
//      columnName 需要做查询的字段名
//}
//
log.info("---------data:{data}"),
sqlstr = logic.run("ConditionalGeneration",data),
data.f_create_time=dateTools.getNow2(),
newid=commonTools.getUUID(),
log.info("当前时间{data.f_create_time}"),
//查询已在安检计划中的地址数量
arrs = sql.querySQL("v3_safecheck_sql", "
    select ui.f_user_name,ua.f_address,ua.id
    from t_user_address ua
    join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
    left join t_meter_book mb on mb.id=uf.f_meter_book_num
    left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
    where {sqlstr} and ua.f_plan_id is not null and (uf.f_table_state='正常' or uf.f_table_state='停用')
    "),
// 新增item数据
sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_plan_item (
                            id,
                            f_userinfo_code,
                            f_userinfoid,
                            f_idnumber,
                            f_slice_area,
                            f_street,
                            f_residential_area,
                            f_building,
                            f_unit,
                            f_floor,
                            f_room,
                            f_address,
                            f_state,
                            f_upload_state,
                            f_plan_id,
                            f_user_name,
                            f_user_phone,
                            f_user_type,
                            f_approved,
                            f_no_checkplan,
                            f_last_check_state,
                            f_last_check_date,
                            f_repair_approved,
                            f_filialeid,
                            f_create_time,
                            version,
                            f_last_check_result,
                            f_comments,
                            f_addressid,
                            f_safecheck_type,
                            f_subscribe_date,
                            f_last_check_date2,
                            f_last_check_state2,
                            f_prearranged_date
                            ) SELECT
                             {ENV.dbType == "sqlserver":"newId()", "SUBSTR(sys_guid(),0)"},
                            ui.f_userinfo_code,
                            ui.f_userinfo_id,
                            ui.f_idnumber,
                            ua.f_slice_area,
                            ua.f_street,
                            ua.f_residential_area,
                            ua.f_building,
                            ua.f_unit,
                            ua.f_floor,
                            ua.f_room,
                            ua.f_address,
                            '未检',
                            '未传',
                            '{data.f_plan_id}',
                            ui.f_user_name,
                            ui.f_user_phone,
                            uf.f_user_type,
                            '未审核',
                            '有计划安检',
                            ua.f_last_check_state,
                            ua.f_last_check_date,
                            '未审核',
                            '{data.f_filialeid}',
                            '{data.f_create_time}',
                            ui.version,
                            ua.f_last_check_result,
                            ui.f_comments,
                            ua.id,
                            '{data.f_safecheck_type}',
                            '{data.f_subscribe_date}',
                            ua.f_last_check_date,
                            ua.f_last_check_state,
                            '{data.f_prearranged_date}'
                            FROM
                                t_user_address ua
                               join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
                               left join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
                               left join t_meter_book mb on mb.id=uf.f_meter_book_num
                               left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
                            WHERE {sqlstr}
                                  and (uf.f_table_state='正常' or uf.f_table_state='停用')
"),
log.info("结果---{arrs}"),
                 //生成对应meter表信息
                 sql.execSQL("v3_safecheck_sql", "insert into t_plan_item_meter (id,f_weizhi,f_maker,f_diaphgram_size,f_aliasname,f_meter_type,f_prior_reading,f_meter_no,f_flow_direction,f_metertitles,f_metergasnums,f_plan_item_id)
                          select
                            {ENV.dbType == "sqlserver":"newId()", "SUBSTR(sys_guid(),0)"},
                            f_position AS f_weizhi,
                            gb.f_manufacturers AS f_maker,
                            gm.f_meter_style AS f_diaphgram_size,
                            gb.f_meter_brand AS f_aliasname,
                            gm.f_type AS f_meter_type,
                            f_meter_base AS f_prior_reading,
                            uf.f_meternumber AS f_meter_no,
                            uf.f_aroundmeter AS f_flow_direction,
                            uf.f_metertitles As f_metertitles,
                            uf.f_meter_base As  f_metergasnums,
                             cpi.id   As  f_plan_item_id
                          FROM
                            t_user_address ua
                            join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
                            left join t_userfiles uf on ua.f_userinfo_id=uf.f_userinfo_id
                            left join t_meter_book mb on mb.id=uf.f_meter_book_num
                            left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
                            left join t_check_plan_item cpi on uf.f_userinfo_id = cpi.f_userinfoid
                            LEFT JOIN t_gasbrand gb ON uf.f_gasbrand_id = gb.id
                            LEFT JOIN  t_gasmodel gm on uf.f_gasmodel_id = gm.id
                          WHERE
                            {sqlstr} and ua.f_plan_id is null
                            and (uf.f_table_state='正常' or uf.f_table_state='停用')
                 "),

// 添加派单动作，注意继承关系
sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_acitivity(serviceid,f_service_acitivity_type,f_batch_number)
            SELECT
                it.id,
                '派单',
                '{newid}'
             from t_check_plan_item it
             join t_user_address ua on it.f_addressid=ua.id
             join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
	        join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
	        left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
	        left join t_meter_book mb on mb.id=uf.f_meter_book_num
             WHERE it.f_plan_id='{data.f_plan_id}' and {sqlstr} and ua.f_plan_id is null and (uf.f_table_state='正常' or uf.f_table_state='停用')
"),
sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_send (id,serviceid,f_service_acitivity_type,f_date,f_meetunit,f_send_planid,f_send_planname,f_batch_number)
         SELECT
            sa.id,
            sa.serviceid,
            sa.f_service_acitivity_type,
            '{data.f_create_time}',
            cp.f_checker,
            cp.id,
            cp.f_plan_name,
            '{newid}'
          from t_check_acitivity sa
          join t_check_plan_item it on sa.serviceid=it.id
          join t_check_plan cp on it.f_plan_id=cp.id
          where sa.f_batch_number='{newid}'
"),
//最后修改地址中的安检计划id
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version=null,f_plan_id='{data.f_plan_id}'
    where  id in ( select ua.id from t_user_address ua
    join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
    left join t_meter_book mb on mb.id=uf.f_meter_book_num
    left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
    where {sqlstr} and ua.f_plan_id is null and (uf.f_table_state='正常' or uf.f_table_state='停用'))"),

{code:200,result:arrs}



