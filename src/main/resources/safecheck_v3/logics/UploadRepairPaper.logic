log.info("-------------data:{data}-------------"),
data.devices.each(
	entity.partialSave(row.EntityType, row)
),

data.paper.f_repair_approved == "打回":
(sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_repaired = '{data.paper.f_repaired}', f_repaired_uploaded = '已传'
, f_repair_approved='重修',f_repair_date = '{data.paper.f_repair_date}'  where f_check_item_id = '{data.f_check_item_id}'")),
(sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_repaired = '{data.paper.f_repaired}', f_repaired_uploaded = '已传'
,f_repair_date = '{data.paper.f_repair_date}' where f_check_item_id = '{data.paper.f_check_item_id}'")),


{code:200}
