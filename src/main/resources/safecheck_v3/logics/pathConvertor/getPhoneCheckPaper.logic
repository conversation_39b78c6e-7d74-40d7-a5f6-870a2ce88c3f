// v3手机端专用 ======= 手机端获取安检单path转logic
list = sql.action({sql: "select * from t_check_paper where {data.condition}"}),
log.debug("list======{list}"),
paper=list.data,
paper.length()>0:(
     imgsData= sql.action({sql: "
        select * from t_paper_overall_imgs where f_paper_id='{paper[0].id}'
    "}),
    imgs=imgsData.data,
    pd_list_data = sql.action({sql: "
        select * from t_paper_devices where f_paper_id='{paper[0].id}'
    "}),
    pd_list=pd_list_data.data,
    di_list_data = sql.action({sql: "
        select di.* from t_paper_devices pd
            left join t_devices_items di on pd.id=di.f_device_id
            where pd.f_paper_id='{paper[0].id}'
    "}),
    di_list=di_list_data.data,
    dil_list_data = sql.action({sql: "
        select dil.* from t_paper_devices pd
        left join t_devices_items di on pd.id=di.f_device_id
        left join t_devices_items_lists dil on di.id=dil.f_item_id
        where pd.f_paper_id='{paper[0].id}'
    "}),
    dil_list=dil_list_data.data,
    di_list = EmptyJSONIds.mergeJsonArrays(di_list,"id","f_item_lists",dil_list,"f_item_id"),
    pd_list = EmptyJSONIds.mergeJsonArrays(pd_list,"id","f_items",di_list,"f_device_id"),
    paper[0].f_devices = pd_list,
    paper[0].f_overall_imgs = imgs
),null,
{data:paper}
