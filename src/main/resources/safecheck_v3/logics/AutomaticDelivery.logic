//安检册自动下发
checkBook=sql.querySQL("v3_safecheck_sql", "select  id,f_check_book_name,f_checker_name,f_checker_id,
f_run_date,f_orgid ,f_create_person,f_around_unit,f_around_time from t_check_book
where CONVERT(varchar(10),cast(f_run_date as datetime),120)='{dateTools.getNow("yyyy-MM-dd")}'
"),
log.info("安检册是{checkBook}"),
checkBook.each(
log.info("安检册数据是{row}"),
//创建安检计划并下发
planId=commonTools.getUUID(),
plan={
id:planId,
f_plan_name:"{dateTools.getNowYear()}年{dateTools.getNowMonth()}月{row.f_check_book_name}",
f_checker:row.f_checker_name,
f_checker_id:row.f_checker_id,
f_plan_year:dateTools.getNowYear(),
f_plan_month:dateTools.getNowMonth(),
f_plan_type:"预约计划",
f_issued:"是",
f_subcompany:"阳春博能",
f_filialeid:row.f_orgid,
f_create_operator:row.f_create_person,
f_create_time:dateTools.getNow2(),
f_safecheck_type:"年度普检",
f_book_id:row.id
},
logic.run("createCheckPlan1",plan),
param={
condition:"ua.f_check_book_id='{row.id}'",
f_plan_id:planId,
f_subscribe_date:dateTools.getNow2(),
f_safecheck_type:"年度普检",
f_filialeid:row.f_orgid
},
//将用户加入计划
logic.run("MoveInPlan",param),
//回更安检册
time=DateUtil.getNowAdd(row.f_around_unit,row.f_around_time),
log.info("时间是{time}"),
 sql.execSQL("v3_safecheck_sql", "update t_check_book set  f_run_date='{time}',f_last_run_date = '{dateTools.getNow2()}'
       where id='{row.id}' ")
),
200

