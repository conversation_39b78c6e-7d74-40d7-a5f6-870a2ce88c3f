log.info("uploadAll==>进入批量上传: {data}"),
successcount = 0,
failedcount = 0,
invalidcount = 0,
state = "",

data.rows.each(
    log.info("开始上传工单：{row}"),
    con=" f_check_item_id='{row.id}'",
    res=path.action({alias: "getInspection", data: {condition:con}}),
    //res = logic.run("FetchCheckPaper",data:{id:row.id}),
    log.info("查询paper结果==》{res}"),
    params = logic.run("UpInspectionPaper",{row:res.data[0],f_check_item_id:res.data[0].f_check_item_id,f_checker_id:res.data[0].f_checker_id}),
    log.info("上传结果：{params}"),

	params.code == 200 :(
        successcount = successcount + 1
	),(
	    params.code == 309 : (
            invalidcount = invalidcount + 1
	    ), (
	        failedcount = failedcount + 1
	    )
	)

),

log.info("uploadAll==>批量上传结束"),
{
    state:0,
    successcount: successcount,
    invalidcount: invalidcount,
    failedcount: failedcount
}
