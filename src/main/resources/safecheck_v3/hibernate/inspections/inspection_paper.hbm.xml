<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--巡检单-->
    <class entity-name="t_inspection_paper" table="t_inspection_paper">
        <id name="id" type="string">
            <generator class="uuid.hex"/>
        </id>
        <!-- 巡检项 id   一对多外键 -->
        <property name="f_check_item_id" type="string"/>
        <!-- 巡检计划 id -->
        <property name="f_check_plan_id" type="string"/>
        <!-- 巡检人id -->
        <property name="f_checker_id" type="string"/>
        <!-- 巡检是否完成 -->
        <property name="f_complete" type="string"/>
        <!-- 用户编号code -->
        <property name="f_userinfo_code" type="string"/>
        <!-- 预约日期 -->
        <property name="f_prearranged_date" type="string"/>
        <!-- 有无计划巡检 -->
        <property name="f_no_checkplan" type="string"/>
        <!-- 巡检人名称 -->
        <property name="f_checker_name" type="string"/>
        <!-- 巡检基本信息 -->
        <!-- 入户时间 YYYY-MM-dd HH:mm:ss-->
        <property name="f_onsite_time" type="string"/>
        <!-- 离开时间 YYYY-MM-dd HH:mm:ss-->
        <property name="f_offsite_time" type="string"/>
        <!-- 入户状态 未用天然气、到访不遇、拒绝入户、入户-->
        <property name="f_entry_status" type="string"/>
        <!--是否使用天然气 已使用/未使用-->
        <property name="f_usegas" type="string"/>
        <!-- 巡检状态 未检 已检  -->
        <property name="f_state" type="string"/>
        <!-- 上传状态 -->
        <property name="f_upload_state" type="string"/>
        <!-- 未使用天然气 照片-->
        <property name="f_nongasuser_path" type="string"/>
        <!-- 到访不遇 照片-->
        <property name="f_noanswer_path" type="string"/>
        <!-- 拒检 照片-->
        <property name="f_rejectcheck_path" type="string"/>
        <!-- 无燃气具 -->
        <property name="f_no_gas_device" type="string"/>
        <!-- 巡检类型 民用巡检、非民用巡检-->
        <property name="f_check_type" type="string"/>

        <!-- 用户基本信息 -->
        <!-- 所属子公司 -->
        <property name="f_subcompany" type="string"/>
        <!-- 用户名-->
        <property name="f_user_name" type="string"/>
        <!-- 用户电话-->
        <property name="f_user_phone" type="string"/>
        <!-- 用户ID-->
        <property name="f_userinfoid" type="string"/>
        <!-- 卡号 -->
        <property name="f_card_id" type="string"/>
        <!-- 六级地址  -->
        <!--区 -->
        <property name="f_area" type="string"/>
        <!-- 街道 -->
        <property name="f_street" type="string"/>
        <!-- 小区 -->
        <property name="f_residential_area" type="string"/>
        <!-- 楼号 -->
        <property name="f_building" type="string"/>
        <!-- 单元-->
        <property name="f_unit" type="string"/>
        <!-- 楼层 -->
        <property name="f_floor" type="string"/>
        <!-- 门牌号-->
        <property name="f_room" type="string"/>
        <!-- 详细地址-->
        <property name="f_address" type="string"/>
        <!-- 满意度 -->
        <!-- 巡检整体照片 -->
        <!--<property name="f_overall_path" type="string" />-->
        <set name="f_overall_imgs" cascade="delete">
            <key column="f_paper_id" on-delete="noaction"/>
            <one-to-many entity-name="t_inspection_imgs" not-found="exception"/>
        </set>
        <!-- 巡检整体照片2 -->
        <property name="f_overall2_path" type="string"/>
        <!-- 经度 -->
        <property name="f_longitude" type="string"/>
        <!-- 纬度 -->
        <property name="f_latitude" type="string"/>
        <!--巡检结果-->
        <property name="f_defect_content" type="string" length="4096"/>
        <!--公司机构ids -->
        <property name="f_filialeids" type="string"/>
        <!--所属公司 -->
        <property name="f_filialeid" type="string"/>

        <!-- 身份证号码 -->
        <property name="f_idnumber" type="string"/>
        <!-- 冗余关联计划列 -->
        <property name="f_plan_id" type="string"/>
        <!-- 维修单id -->
        <!--<property name="f_repairorder_id" type="int" />-->
        <!-- 备注信息 -->
        <property name="f_comments" type="string" length="255"/>
        <!--用户性质 -->
        <property name="f_userproperties" type="string" length="50"/>
        <!-- 巡检单上传时间 -->
        <property name="f_upload_date" type="string"/>
        <!-- 巡检类型-->
        <property name="f_safecheck_type" type="string"/>
        <!-- 巡检项 -->
        <set name="f_devices" cascade="delete">
            <key column="f_paper_id" on-delete="noaction"/>
            <one-to-many entity-name="t_inspection_devices" not-found="exception"/>
        </set>
    </class>
</hibernate-mapping>
