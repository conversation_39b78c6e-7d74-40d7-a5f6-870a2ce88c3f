<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--维修登记-->
    <class entity-name="t_repair_record" table="t_repair_record">
        <id name="id" type="int">
            <generator class="native"/>
        </id>
        <!-- 用户编号 -->
        <property name="f_userinfo_id" type="string"/>
        <!-- 用户姓名 -->
        <property name="f_user_name" type="string"/>
        <!-- 联系电话 -->
        <property name="f_user_phone" type="string"/>
        <!-- 派单编号 -->
        <property name="f_send_no" type="string"/>
        <!-- 报修时间 -->
        <property name="f_apply_date" type="string"/>
        <!-- 详细地址 -->
        <property name="f_address" type="string"/>
        <!-- 到达时间 -->
        <property name="f_insite_date" type="string"/>
        <!-- 离开时间 -->
        <property name="f_offsite_date" type="string"/>
        <!-- 报修原因 -->
        <property name="f_apply_reason" type="string"/>
        <!-- 维修内容 -->
        <property name="f_repair_content" type="string"/>
        <!-- 表号 -->
        <property name="f_meter_no" type="string"/>
        <!-- 表向 -->
        <property name="f_meter_direction" type="string"/>
        <!-- 气表表封号 -->
        <property name="f_meter_title_no" type="string"/>
        <!-- 剩余气量/金额 -->
        <property name="f_surplus_gm" type="string"/>
        <!-- 表型 -->
        <property name="f_meter_model" type="string"/>
        <!-- 使用气量 -->
        <property name="f_used_gas" type="string"/>
        <!-- 用户意见 -->
        <property name="f_advise" type="string"/>
        <!-- 用户满意度 -->
        <property name="f_satisfaction" type="string"/>
        <!-- 维修人员签字 -->
        <property name="f_repair_path" type="string"/>
        <!-- 用户签字 -->
        <property name="f_user_path" type="string"/>
        <!-- 备注 -->
        <property name="f_note" type="string"/>
        <!-- 操作人员 -->
        <property name="f_operator" type="string"/>
        <property name="f_operator_id" type="string"/>
        <!-- 创建时间 -->
        <property name="f_create_time" type="string"/>
        <!-- 维修前照片 -->
        <property name="f_pre_repair_path" type="string"/>
        <property name="f_pre_repair1_path" type="string"/>
        <property name="f_pre_repair2_path" type="string"/>
        <!-- 维修后照片 -->
        <property name="f_repaired_path" type="string"/>
        <property name="f_repaired1_path" type="string"/>
        <property name="f_repaired2_path" type="string"/>
        <!-- 纸质维修单照片 -->
        <property name="f_paper_path" type="string"/>
        <property name="f_paper1_path" type="string"/>
        <property name="f_paper2_path" type="string"/>
        <!-- 是否清零 -->
        <property name="f_is_clear" type="string"/>
    </class>
</hibernate-mapping>
