<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	<!--计划项-->
	<class entity-name="t_plan_item_device" table="t_plan_item_device">
   		<id name="id" type="string">
			<generator class="assigned"/>
		</id>
		<!-- 档案id -->
		<property name="f_userinfoid" type="string" />
		<!-- 设备名称 -->
		<property name="f_name" type="string" />
		<!-- 设备品牌 -->
		<property name="f_brand" type="string" />
		<!-- 设备型号 -->
		<property name="f_heatingboiler_model" type="string" />
		<!--设备状态-->
		<property name="f_devices_state" type="string"/>
		<!--生产日期-->
		<property name="f_making_date" type="string"/>
		<!-- 关联 -->
		<property name="f_plan_item_id" type="string" />
	</class>
</hibernate-mapping>
