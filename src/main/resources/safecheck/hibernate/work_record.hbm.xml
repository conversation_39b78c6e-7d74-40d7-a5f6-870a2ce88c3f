<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	<!--上班打卡记录-->
	<class entity-name="t_work_record" table="t_work_record">
   		<id name="id" type="int">
            <generator class="native"/>
		</id>
		<!-- 员工id -->
		<property name="f_user_id" type="string" length="50" />
		<!-- 员工姓名-->
		<property name="f_user_name" type="string" length="50"/>
		<!-- 上班打卡时间-->
		<property name="f_onsite_time" type="string" length="50"/>
		<!-- 下班打卡时间-->
		<property name="f_offsite_time" type="string" length="50"/>
	</class>
</hibernate-mapping>
