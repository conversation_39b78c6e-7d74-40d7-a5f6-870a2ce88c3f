<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	<!--上下班签到-->
	<class entity-name="t_user_signin" table="t_user_signin">
   		<id name="id" type="string">
            <generator class="uuid"/>
		</id>
		<!-- 签到日期 -->
		<property name="f_date" type="date" />
		<!-- 用户名 -->
		<property name="f_user_name" type="string"/>
		<!-- 用户id-->
		<property name="f_userinfoid"  type="string"/>
		<!-- 上班签到时间-->
		<property name="f_signin_time"  type="date"/>
		<!-- 下班签到时间 -->
		<property name="f_knockoff_time"  type="date"/>
		<!-- 备注 -->
		<property name="f_remark" type="string" />
	</class>
</hibernate-mapping>
