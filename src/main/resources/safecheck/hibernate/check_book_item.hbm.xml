<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
    <!--安检地址，给地址表添加安检内容-->
    <class entity-name="t_check_book_item" table="t_check_book_item">
        <id name="id" type="int">
            <generator class="native"/>
        </id>
        <!-- 安检册名称 -->
        <property name="f_check_book_name" type="string" length="255"/>
        <!-- 安检册id-->
        <property name="f_check_book_id" type="string" length="255"/>
        <!-- 安检册类型-->
        <property name="f_type" type="string" length="255"/>
        <!-- 安检册所属安检员 -->
        <property name="f_checker_name" type="string" length="255"/>
        <!-- 客户名称 -->
        <property name="f_user_name" type="string" length="255"/>
        <!-- 客户编号 -->
        <property name="f_userinfo_code" type="string" length="255"/>
        <!-- 小区编号 -->
        <property name="f_residential_area_id" type="string" length="255"/>
        <!-- 街道 -->
        <property name="f_street" type="string" length="255"/>
        <!-- 小区 -->
        <property name="f_residential_area" type="string" length="255"/>
        <!-- 电话 -->
        <property name="f_user_phone" type="string" length="255"/>
        <!-- 地址 -->
        <property name="f_address" type="string" length="255"/>
        <!-- 安检科室id -->
        <property name="f_depid" type="string" length="255"/>
        <!-- 所属公司id -->
        <property name="f_orgid" type="string" length="255" />
    </class>
</hibernate-mapping>
