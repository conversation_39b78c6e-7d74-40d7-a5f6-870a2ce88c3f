<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--回访信息表-->
    <class entity-name="t_safe_visit" table="t_safe_visit">
        <!-- id -->
        <id name="id" type="int" column="id">
            <generator class="native"></generator>
        </id>
        <!--安检单编号 -->
        <property name="f_paper_id" type="string" length="100"/>
        <!--回访员id -->
        <property name="f_visitor_id" type="string" length="50"/>
        <!--回访员 -->
        <property name="f_visitor" type="string" length="50"/>
        <!--回访时间 -->
        <property name="f_visit_date" type="string"/>
        <!--服务满意 -->
        <property name="f_service_score" type="string" length="255"/>
        <!--质量满意 -->
        <property name="f_quality_score" type="string" length="255"/>
        <!-- 联系电话 -->
        <property name="f_phone" type="string" length="50"/>
        <!--客户意见 -->
        <property name="f_remarks" type="string" length="1000"/>
        <!--备注 -->
        <property name="f_bz" type="string" length="1000"/>
        <!-- 回访情况（null, 未完成，已完成） -->
        <property name="f_visit" type="string" length="50"/>
        <!-- 回访方式 -->
        <property name="f_visit_method" type="string" length="50"/>
    </class>
</hibernate-mapping>
