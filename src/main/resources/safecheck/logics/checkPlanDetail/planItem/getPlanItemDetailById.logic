validate {
    id: {
        required: true
    }
},
list = [],
userinfo = [],
list=sql.querySQL("getSecurityPlanName","
        select * from t_v4_check_plan_item where id={data.id}
"),
list.length()>0:(
    userinfo = sql.querySQL("查询item所属用户表信息", "
        SELECT
        	uf.f_user_id As id, --表档案id
            ui.f_user_phone,--用户电话
        	f_position, --安装位置
        	gb.f_manufacturers, --气表厂家
        	gm.f_meter_style, --气表型号
        	gb.f_meter_brand, --气表品牌
        	gb.f_meter_type, --气表类型
        	f_meter_base, --气表底数
        	uf.f_meternumber, --表号
        	f_aroundmeter, --左右表
        	f_metertitles, --表封号
        	f_meter_base, --气表底数
            ui.f_ins_stop_date,--上期保费截止时间
        	uf.f_initial_base, --表基数
        	uf.f_total_fee, --累购金额
            (select sum(f_total_fee - isnull(f_newtable_fillfee,0)) from t_userfiles where f_userinfo_id = uf.f_userinfo_id) as f_total_fee_all,
        	case when uf.f_meter_classify='机表' then uf.f_meter_base else uf.f_total_gas end f_total_gas,--累购气量
        	gb.f_collection_type, --购气类型(按金额，按气量)
        	f_meter_classify,
            ui.f_last_check_state,
            ui.f_last_checker as f_checker_name,
            ui.f_last_check_date,
            ui.f_last_check_result,
        	uf.version,
            case when uf.f_meter_classify='机表' then uf.f_bqf_state else (case when mr.f_valveState = '0' then '开阀' else '关阀' end) end f_valve_state,--开关阀状态
            uf.f_off_valve_reason,--开关阀原因
        	uf.f_balance_amount,
            uf.f_defendcard,
            uf.f_defendcard_two,
            ui.f_paper_name,--企业名称(非民用)
            ui.f_idnumber,
            uf.f_balance_gas,
            ui.f_taxpayer_id,--纳税人识别编号(非民用)
            ui.f_contact_user,--联系人(非民用)
            uf.f_userfiles_id,
            sp.f_price_name,
            uf.f_gasproperties, --用气性质
            uf.f_usage
        FROM
        	t_userfiles uf
        left join t_userinfo ui on	 uf.f_userinfo_id = ui.f_userinfo_id
        LEFT JOIN t_user_address u on uf.f_userinfo_id=u.f_userinfo_id
        --left join t_userfees tuf on tuf.f_userinfo_id=uf.f_userinfo_id
        LEFT JOIN t_gasbrand gb ON uf.f_gasbrand_id = gb.id
        LEFT JOIN t_gasmodel gm on uf.f_gasmodel_id = gm.id
        left join  t_meteread mr with (nolock) on uf.f_meteread_maxid = mr.id
        left join (select * from t_stairprice
           where getdate()>=f_perform_date and getdate()<=f_end_date
         and f_state ='有效')sp on uf.f_price_id=sp.f_price_id
        WHERE
        	uf.f_userinfo_id = '{f_userid}' and uf.f_table_state in ('正常','停用','待开通')

    ")
),null,
{item:list,userinfo:userinfo}
