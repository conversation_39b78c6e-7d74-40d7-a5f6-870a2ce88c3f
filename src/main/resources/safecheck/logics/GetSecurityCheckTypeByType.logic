log.info("getSecurityCheckTypeByType接收参数{data}"),
// 查找 type 版本最新的数据
result = sql.querySQL("getSecurityCheckTypeByType",
"
    SELECT th.*
    FROM t_security_check_type_history th
    INNER JOIN (
        SELECT version AS version, id
        FROM t_security_check_type
        WHERE f_safecheck_type = '{data.f_safecheck_type}' and f_user_type = '{data.f_user_type}'
        {data.has("f_orgid"):" and f_orgid = '{data.f_orgid}'",""}
    ) t
    ON th.version = t.version AND th.f_security_check_type_id = t.id
"
),

result
