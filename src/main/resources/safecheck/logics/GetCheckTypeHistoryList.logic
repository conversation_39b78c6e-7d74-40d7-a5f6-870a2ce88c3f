validate {
    version: {
        required: true
    },
    f_security_check_type_id: {
        required: true
    }
},

log.info("getCheckTypeHistoryList接收参数: {data}"),

result = sql.querySQL("getCheckTypeHistoryListSQL",
"
    SELECT
        *
    FROM
        t_security_check_type_history
    WHERE
        f_security_check_type_id = {data.f_security_check_type_id}
        AND version != {data.version}
    ORDER BY
        version DESC
"
),

result
