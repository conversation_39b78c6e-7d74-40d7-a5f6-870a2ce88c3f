// 新增安检册计划
validate {
    // 这是一个必选参数示例，如果调用方没有传入该参数，将抛出异常，异常说明为：参数校验失败:xxx不能为null
    checkListDetail: {
        // 安检册名称
        f_list_name: {
            required: true,
            message: "安检册名称不能为空"
        },
        //安检单模板
        f_safechecktype_id: {
            required: true,
            message: "安检单模板不能为空"
        },
        //起始时间
        f_efficient_strat: {
            required: true,
            message: "起始时间不能为空"
        },
        // 截止时间
        f_efficient_end: {
            required: true,
            message: "截止时间不能为空"
        },
        // 执行周期(天)
        f_exe_cycle: {
            required: true,
            message: "执行周期(天)不能为空"
        }
    }
},
data.checkListDetail.f_efficient_strat > data.checkListDetail.f_efficient_end :(
    throw {
            msg: $起始时间需要小于截至时间$,
            status: 599
    }
),null,
result={code:200},
plan = entity.partialSave("t_v4_check_list", data.checkListDetail),
checkerList= [],
data.type.equals("修改"):(
    sql.execSQL("删除安检人员", "
        delete from t_v4_plan_relation
        where t_v4_plan_relation.f_plan_id = {plan.id} and t_v4_plan_relation.f_type = 1
    ")
),null,
data.checkerList.f_checker != null:(
    data.checkerList.f_checker.each(
        checkerList.put({
            f_plan_id:plan.id,
            f_user_id:row,
            f_type:1
        })
    ),
    entity.partialSave("t_v4_plan_relation", checkerList)
),null,
result.data=plan,
result
