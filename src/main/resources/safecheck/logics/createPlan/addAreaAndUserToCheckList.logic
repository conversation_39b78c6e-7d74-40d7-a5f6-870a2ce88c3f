// 将用户添加到安检册中
validate {
    rows: { // 条件数组
        required: true
    },
    checkListId: { //安检册id
        required: true
    },
    type: { //类型 小区: area / 用户: user
        required: true
    },
    filialeid: { // 分公司编号
        required: true
    },
},
result = {code:200},
ids = commonTools.union(data.rows),
"area".equals(data.type):(
    condition = "area.id in ({ids})"
),(
    condition = "ui.f_userinfo_id in ({ids})"
),
users = sql.querySQL("getUserInfoIdByResidentialArea","
        select {data.checkListId} f_list_id,ui.f_userinfo_id from t_userinfo ui
        left join t_user_address ua on ui.f_userinfo_id=ua.f_userinfo_id
        left join t_area area on area.id=ua.f_residential_area_id
        left join t_v4_checklist_user cu on ui.f_userinfo_id=cu.f_userinfo_id
        where {condition} and cu.f_userinfo_id is null and ui.f_orgid={data.filialeid}
    "),
users.length()>0:(
    entity.partialSave("t_v4_checklist_user", users)
),null,
result.data={successCon:users.length()},
result
