result={},
log.info(data.id),
check_list = sql.querySQL("获取安检手册","
        select
            *
        FROM
            t_v4_check_list
        where
            t_v4_check_list.id = {data.id}
"),
plan_relation = sql.querySQL("获取安检人员","
        select
            *
        FROM
            t_v4_plan_relation
        where
            t_v4_plan_relation.f_plan_id = {data.id} and t_v4_plan_relation.f_type = 1
"),
plan_relation_list = [],
plan_relation.each(
    plan_relation_list.put(row.f_user_id)
),
result = {checkListDetail: check_list[0].length()>0: check_list[0],[] , checkerList: {f_checker: plan_relation_list}},
result
