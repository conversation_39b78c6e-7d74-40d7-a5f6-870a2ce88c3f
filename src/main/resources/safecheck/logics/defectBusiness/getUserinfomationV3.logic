restul= sql.querySQL("selectTCP","select * from t_check_paper where  id = '{data.id}' "),
f_devices = sql.querySQL("selectTPD","select * from t_paper_devices where f_paper_id = '{restul[0].id}'"),
f_devices.each(
  row.f_items = sql.querySQL("selectTDI","select * from t_devices_items where f_device_id = '{row.id}'"),
  row.f_items.each(
    f_item_lists = sql.querySQL("selectTDIL","select * from t_devices_items_lists where f_item_id = '{row.id}'"),
    f_item_lists.length() > 0 :(
        row.f_item_value = f_item_lists[0].f_item_value
    ),(
        row.f_item_value = "无"
    )
  )
),
f_devices