<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--安检单对应的计数信息-->
    <class entity-name="t_v4_paper_files" table="t_v4_paper_files">
        <id name="id" type="int">
            <generator class="native">
                <param name="native">SEQ_SAFE_PAPERFILES_ID</param>
            </generator>
        </id>
        <!-- 安检单id-->
        <property name="f_business_id" type="int"/>
        <!-- 业务类型 入户安检/隐患照片/隐患消除-->
        <property name="f_business_type" type="string" length="50"/>
        <!-- 附件字段 此字段自定义 例如 到访不遇/拒检/入户/-->
        <property name="f_fields" type="string" length="20"/>
        <!-- 文件类型 -->
        <property name="f_type" type="string" length="20"/>
        <!-- 文件名称-->
        <property name="f_file_name" type="string"/>
        <!-- 文件id t_files 关联-->
        <property name="f_file_id" type="string"/>
    </class>
</hibernate-mapping>
