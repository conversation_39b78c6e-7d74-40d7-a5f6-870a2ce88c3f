--人员统计
select
    *,
    trunc(register_total/total,2) register_rate --入户率
from (
      select
          u.id,
          u.name,
          count(1) total,--计划总数
          sum(case when f_state='已检' and f_last_check_state='入户' then else end) register_total,--计划中入户数
          sum(case when f_state='已检' and f_last_check_state='到访不遇' then else end) out_total,--计划中到访不遇数
          sum(case when f_state='已检' and f_last_check_state='拒检' then else end) reject_total,--计划中拒检数
          sum(case when f_no_checkplan=0 and f_last_check_state='入户' then else end) no_register_total,--计划中入户数
          sum(case when f_no_checkplan=0 and f_last_check_state='到访不遇' then else end) no_out_total,--无计划到访不遇数
          sum(case when f_no_checkplan=0 and f_last_check_state='拒检' then else end) no_reject_total,--无计划拒检数
          sum(case when f_last_is_defect = '是' and f_last_check_state='入户' then else end) defect_total,--总有隐患数
          sum(case when f_last_is_defect = '否' and f_last_check_state='入户' then else end) no_defect_total,--总无隐患数
      from t_v4_check_plan_item cpi
               left join t_v4_check_plan cp on cpi.f_plan_id=cp.id
               left join t_v4_plan_relation pr on pr.f_plan_id=cp.id
               left join t_user u on pr.f_user_id=u.id
      where (cp.f_isshare = 0 or cp.f_plan_id is null) and {condition}
      group by u.id,u.name
  ) temp
