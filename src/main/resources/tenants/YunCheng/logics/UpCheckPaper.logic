log.info("uploadPaper:------进入数据保存过程-----"),
log.info( "进入上传: {data}"),
data = data.row,
// 上传图片
url = "{context.cc_base_url}/af-telephone/rs",
resolve = UploadPic.uploadPic(url,data),
resolve == null :(
    log.info( "上传图片失败"),
    message = UploadPlugin.getResObj().message,
    return {code: 710, msg: message}
),null,
log.info( "上传图片成功"),
paperState="正常",
isCheck = resolve.data.paper.f_no_checkplan != "无计划安检" && resolve.data.paper.f_no_checkplan != "复检",
isCheck == true :(
    d0 = {
        data: {
            id: resolve.data.paper.f_check_item_id
        }
    },
    log.info( "参数===》{d0}"),
    resJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/sql/GetCheckItemState",data:d0.toString()},
    res = restTools.action(resJson),
    log.info( "结果===》{res}"),
    res.data.length() == 0 :(
        log.info( "服务器安检单不存在"),
        return {code: 711, msg: "安检单已被移除"}
    ),null,
    lastState = res.data[0].f_last_check_state,
    paperState = res.data[0].f_state,
    paperDate = res.data[0].f_last_check_date,
    isUploadDay = res.data[0].isupload_day,
    paperStateupload = res.data[0].f_upload_state,
    paperState == "已检" && lastState == "入户" && paperStateupload == "已传" :(
        sql.action({sql: "update t_check_plan_item set f_state='已检',f_upload_state='已传',f_flag=0
            where id = '{data.f_check_item_id}'", cmd: "cmd"
        }),
        return {code:200, msg:"该单已成功入户，请勿重复上传!"}
    ),
    paperState == "已检" && lastState == "到访不遇" && paperDate == resolve.data.paper.f_offsite_time :(
        return {code:200, msg:"该单已上传，请勿重复上传!"}
    ),null,
    paperState == "作废" :(
        return {code: 309, msg: "作废"}
    ),null,
    // 上传工单
    data.f_repaired = "未修",
    data.f_repaired_uploaded = "已传",
    restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/UpdateCheckPaper", data:resolve.toString()},
    log.info("开始上传工单：{data.toString()}"),
    params = restTools.action(restJson),
    log.info("上传结果：{params}"),
    log.info("上传data：{data}"),
    log.info("返回数据version：{params.data.data.f_check_version}"),
    log.info("返回数据paper：---{params.data.data.papers}---{params.data.data.id}"),
    params.code == 200 :(
        log.info("上传data：1"),
        sql.action({sql: "update t_check_plan_item set f_approved = null,f_state='已检',f_upload_state='已传',f_flag=0,f_check_version={params.data.data.f_check_version}
            where id = '{data.f_check_item_id}'", cmd: "cmd"
        }),
        log.info("上传data：2"),
        return {code:200,data:params.data.data.papers,id:params.data.data.id, msg:"上传成功"}
    ),(
        return {code: 712, msg: "网络异常"}
    )
),(
    data.f_repaired = "未修",
    data.f_repaired_uploaded = "已传",
    restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/UpdateCheckPaper", data:resolve.toString()},
    log.info("开始上传工单：{data.toString()}"),
    params = restTools.action(restJson),
    log.info("上传结果：{params}"),
    log.info("上传data：{data}"),
    log.info("返回数据paper：---{params.data.data.papers}---{params.data.data.id}"),
    params.code == 200 :(
        log.info("上传data：1"),
        sql.action({sql: "update t_check_plan_item set f_state='已检',f_upload_state='已传',f_flag=0
            where id = '{data.f_check_item_id}'", cmd: "cmd"
        }),
        log.info("上传data：2"),
        return {code:200,data:params.data.data.papers,id:params.data.data.id, msg:"上传成功"}
    ),(
        return {code: 712, msg: "网络异常"}
    )
)
