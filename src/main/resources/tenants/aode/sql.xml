<cfg>
    <!--*****************************************app*****************************************pc-->
    <!-- 查找安检单-->
    <sql alias='查找安检单TS' path='TS/QueryCheckPaperTS.sql' dataSource="slave"/>
    <sql alias='QueryCheckPaperTS' path='TS/QueryCheckPaperTS.sql' dataSource="slave"/>
    <sql alias='getRemindTS' path='TS/getRemindTS.sql'/>
    <sql alias='根据安检员查询用户档案TS' path='TS/FetchUserFileByOrgCheckerTS.sql' dataSource="slave"/>
    <sql alias='FetchUserFileByOrgCheckerTS' path='TS/FetchUserFileByOrgCheckerTS.sql' dataSource="slave"/>
    <sql alias='getPlanItemCountByCheckerTS' path='TS/getPlanItemCountByCheckerTS.sql' dataSource="slave"/>
    <sql alias='GetCheckPaperByDefectTS' path='TS/GetCheckPaperByDefectTS.sql' dataSource="slave"/>
    <sql alias='GetUserForRepaireTS' path='TS/GetUserForRepaireTS.sql'/>
    <sql alias="getCheckPlanAreaListTS" path="TS/getCheckPlanAreaListTS.sql"/>
    <sql alias='提取用户档案TS' path='TS/FetchUserByCheckerTS.sql'/>
    <sql alias='getYearPlanStatisticsTS' path='TS/getYearPlanStatisticsTS.sql'/>
    <sql alias='getCheckPlanUserListTS' path='TS/getCheckPlanUserListTS.sql'/>
    <!--下拉计划列表-->
    <sql alias="getCheckplanSelectTS" path="TS/getCheckPlanSelectTS.sql"/>
    <!--根据安检单id获取隐患详情-->
    <sql alias='GetDefectByPaperIdTS' path='TS/GetDefectByPaperIdTS.sql'/>
    <!-- 计划计数TS -->
    <sql alias='planCountTS' path='TS/planCountTS.sql'/>
    <!-- 计划项查询 -->
    <sql alias='planItemTS' path='TS/planItemTS.sql'/>
    <!-- 根据年月安检员查询安检项 -->
    <sql alias='FetchPlanItemByYearMonthCheckerIdTS' path='TS/FetchPlanItemByYearMonthCheckerIdTS.sql'/>
    <!-- 手机端用户信息查询-->
    <sql alias='androidGetuserinfoTS' path='TS/AndroidGetUserInfoTS.sql' mobile='true'/>
    <!-- 预约计划下发-->
    <sql alias='预约计划下发TS' path='TS/bespeakcheckplanTS.sql'/>
    <!-- 预约计划下发-->
    <sql alias='查询计划项表信息TS' path='TS/FetchPlanUserfilesTS.sql'/>
    <sql alias='safeCheckgetUserInfoTS' path='TS/getUserInfoTS.sql'/>
    <!-- 专项计划用户查询 -->
    <sql alias='FetchUserFileByOrgNoRuHuSXQZXTS' path='TS/FetchUserFileByOrgNoRuHuSXQZXTS.sql' dataSource="slave"/>
    <!-- 预约计划 未安检筛选 -->
    <sql alias='FetchUserFileByOrgNoRuHuSXQTS' path='TS/FetchUserFileByOrgNoRuHuSXQTS.sql' dataSource="slave"/>
    <!--日均用气量-->
    <sql alias='getGasDayTS' path='TS/report/getGasDayTS.sql'/>
    <!--新增 TS-->
    <sql alias='按年月人员提取安检计划TS' path='TS/FetchCheckerPlanTS.sql'/>
    <sql alias='getCheckplanTS' path='TS/getCheckPlanTS.sql'/>
    <sql alias='GetNewPlanTS' path='TS/GetNewPlanTS.sql' mobile='true'/>


    <!--*********************************************以上是外部安检使用的sql********************************************************************-->

    <!-- 已下载计划  安卓端使用-->
    <sql alias='已下载计划' path='CheckDownloadedPlan.sql' mobile='true'/>
    <!-- 手机端用户信息查询-->
    <sql alias='androidGetuserinfo' path='AndroidGetUserInfo.sql' mobile='true'/>
    <!-- 手机端已办安检单信息查询-->
    <sql alias='androidGetHasDone' path='AndroidGetHasDone.sql' mobile='true'/>
    <sql alias='androidGetHasDoneTS' path='TS/AndroidGetHasDoneTS.sql' mobile='true'/>

    <sql alias='GetNewPlan' path='GetNewPlan.sql' mobile='true'/>


    <!--*****************************************pc*****************************************pc-->
    <sql alias='用户档案审核查询' path='VerifyUserFileQuery.sql'/>
    <!-- 预约计划-->
    <sql alias='根据安检员查询用户档案' path='FetchUserFileByOrgChecker.sql' dataSource="slave"/>
    <!-- 预约计划-->
    <sql alias='FetchUserFileByOrgChecker' path='FetchUserFileByOrgChecker.sql' dataSource="slave"/>
    <!-- 预约计划 未安检筛选 且 已生成计划-->
    <!-- 预约计划 未安检筛选 -->
    <sql alias='FetchUserFileByOrgNoRuHuSXQ' path='FetchUserFileByOrgNoRuHuSXQ.sql'  dataSource="slave"/>
    <!-- 专项计划用户查询 -->
    <sql alias='FetchUserFileByOrgNoRuHuSXQZX' path='FetchUserFileByOrgNoRuHuSXQZX.sql'   dataSource="slave"/>

    <!-- 计划计数 -->
    <sql alias='planCount' path='planCount.sql'/>

    <!-- 计划项查询 -->
    <sql alias='planItem' path='planItem.sql'/>
    <!-- 民用日现金气量核对 -->
    <sql alias='FindCheckerAnnualPlan' path='FindCheckerAnnualPlan.sql'/>
    <!-- 民用月现金气量核对 -->
    <sql alias='FindAnnualPlanItem' path='FindAnnualPlanItem.sql'/>

    <!-- 查找安检单-->
    <sql alias='查找安检单' path='QueryCheckPaper.sql'   dataSource="slave"/>
    <!-- 查找安检单-->
    <sql alias='QueryCheckPaper' path='QueryCheckPaper.sql'   dataSource="slave"/>

    <!-- 查找维修单-->
    <sql alias='查找维修单' path='QueryRepairPaper.sql' dataSource="slave"/>

    <sql alias='月考勤' path='QueryMonthlyAttendance.sql' dataSource="slave"/>
    <!--安检员手机实时状态  tbd  -->
    <sql alias='checkerphonestate' path='checkerphonestate.sql'/>


    <!-- 获取最新手机状态 -->
    <!--	<sql alias='获取最新手机状态' path='FetchLatestPhoneState.sql' />-->

    <!-- 灶具-->
    <sql alias='灶具使用情况' path='ResidentCooker.sql'/>
    <!-- 壁挂炉-->
    <sql alias='壁挂炉使用情况' path='ResidentFurnace.sql'/>
    <!-- 热水器-->
    <sql alias='热水器使用情况' path='ResidentHeater.sql'/>
    <sql alias='按年月人员提取安检计划' path='FetchCheckerPlan.sql'/>

    <sql alias='汇总查询' path='Splash.sql' dataSource="slave"/>
    <sql alias='汇总明细查询' path='SplashDetail.sql' dataSource="slave"/>
    <sql alias='查询计划项表信息' path='FetchPlanUserfiles.sql'/>

    <sql alias='CheckPaperSearchItem' path='CheckPaperSearchItem.sql'/>

    <sql alias='QueryMonthCheckRate' path='QueryMonthCheckRate.sql'/>
    <sql alias='getCompany' path='getCompany.sql'/>
    <sql alias='getShareCheckPlan' path='getShareCheckPlan.sql'/>
    <sql alias='QueryRepairStatus' path='QueryRepairStatus.sql'/>
    <!-- 安检结果按月统计 -->
    <sql alias='monthSearch' path='monthSearch.sql'  dataSource="slave"/>
    <!-- 安检员已检未检按月统计 -->
    <sql alias='monthSearchByChecker' path='monthSearchByChecker.sql'/>
    <!-- 安检隐患按月统计 -->
    <sql alias='monthSearchByDefect' path='monthSearchByDefect.sql' dataSource="slave"/>
    <!-- 安检率报表 -->
    <!--	<sql alias='SafePlanSale' path='SafePlanSale.sql' />-->

    <!-- 安检单报表查询 -->
    <sql alias='QueryExportPaper' path='QueryExportPaper.sql'/>
    <!-- 安检单综合查询 -->
    <sql alias='QueryExportCheck' path='QueryExportCheck.sql'/>
    <sql alias='getRemind' path='getRemind.sql'/>
    <!--获取该安检单的状态-->
    <sql alias='GetCheckItemState' path='GetCheckItemState.sql'/>
    <sql alias='GetCheckItemStateTS' path='TS/GetCheckItemStateTS.sql'/>
    <!--根据隐患获取安检单-->
    <sql alias='GetCheckPaperByDefect' path='GetCheckPaperByDefect.sql'  dataSource="slave"/>
    <!--根据安检单id获取隐患详情-->
    <sql alias='GetDefectByPaperId' path='GetDefectByPaperId.sql'/>
    <!--安检情况汇总-->
    <sql alias="getCheckPlanAreaList" path="getCheckPlanAreaList.sql"    dataSource="slave"/>
    <sql alias="getCheckPlanUserList" path="getCheckPlanUserList.sql"    dataSource="slave"/>
    <!--下拉计划列表-->
    <sql alias="getCheckplanSelect" path="getCheckPlanSelect.sql"/>
    <!-- 隐患报表 -->
    <sql alias='HiddenSituation' path='HiddenSituation.sql' dataSource="slave"/>
    <!-- 插入建议 -->
    <sql alias='addAdvise' path='addAdvise.sql'/>
    <!--查询安检项和meter信息-->
    <sql alias='getCheckPlanItemByPlanIdTS' path='TS/getCheckPlanItemByPlanIdTS.sql'/>
    <sql alias='getCheckPlanItemByPlanId' path='getCheckPlanItemByPlanId.sql'/>
    <sql alias='getCheckPlanMeterByPlanId' path='getCheckPlanMeterByPlanId.sql'/>
    <sql alias='getCheckPlanMeterByPlanIdTS' path='TS/getCheckPlanMeterByPlanIdTS.sql'/>
    <!--查询年度计划情况-->
    <sql alias='getYearPlanStatistics' path='getYearPlanStatistics.sql'/>
    <!--查询某段时间内安检数量 民用 非民用 其他数量-->
    <sql alias='getPlanItemCountByChecker' path='getPlanItemCountByChecker.sql'   dataSource="slave"/>
    <sql alias='getCheckItemCountByChecker' path='getCheckItemCountByChecker.sql'/>
    <sql alias='getUserItemCountByChecker' path='getUserItemCountByChecker.sql'/>

    <!-- 查询散户计划 -->
    <sql alias='getRetailplan' path='GetRetailplan.sql'/>
    <!-- 查询散户计划用户 -->
    <sql alias='getRetailitem' path='GetRetailitem.sql'/>

    <sql alias='getRpaorImg' path='getRpaorImg.sql'/>

    <sql alias='Get_khrq' path='Get_khrq.sql'/>

    <!--我的newfile-->
    <sql alias='NewfileList' path='Newfile/NewfileList.sql'/>
    <sql alias='NewfileList1' path='Newfile/NewfileList1.sql'/>
    <sql alias='NewfileList2' path='Newfile/NewfileList2.sql'/>
    <sql alias='Newfilepaper' path='Newfile/Newfilepaper.sql'/>
    <!--用户下发情况-->

    <sql alias='report_getAllDefectCountTS' path='TS/report/getAllDefectCountTS.sql'/>
    <sql alias='report_getAllDefectAllTS' path='TS/report/getAllDefectAllTS.sql'/>
    <sql alias='report_getOrgTS' path='TS/report/getOrgTS.sql' dataSource="slave"/>

    <sql alias='UserplanPaper' path='userplan/UserplanPaper.sql'   dataSource="slave"/>
    <sql alias='UserplanList' path='userplan/UserplanList.sql'/>
    <sql alias='report_getAllDefectCount' path='report/getAllDefectCount.sql'/>
    <sql alias='report_getAllDefectAll' path='report/getAllDefectAll.sql'/>
    <sql alias='report_getOrg' path='report/getOrg.sql' dataSource="slave"/>
    <!--	查询年度普检计划-->
    <sql alias='FetchCheckerPlanYear' path='FetchCheckerPlanYear.sql'/>
    <sql alias='FetchCheckItemByOrgChecker' path='FetchCheckItemByOrgChecker.sql'/>
    <sql alias='FetchUserItemByOrgChecker' path='FetchUserItemByOrgChecker.sql'/>
    <sql alias='getGasDay' path='getGasDay.sql'/>
    <sql alias='getGasDayWlw' path='getGasDayWlw.sql'/>
    <!--	<sql alias='avgGasMainAdd' path='avgGasMainAdd.sql' />-->

    <sql alias="FetchAndroidAnnouncement" path="FetchAndroidAnnouncement.sql"/>

    <!--抄表用户列表查询-->
    <sql alias="queryCheckHandplanList" path="QueryCheckHandplanList.sql" dataSource="slave"/>
    <!--上次抄表信息查询-->
    <sql alias="queryCheckMeterReading" path="QueryCheckMeterReading.sql" dataSource="slave"/>
    <!--查询安检抄表记录-->
    <sql alias="searchCheckHandPlan" path="SearchCheckHandPlan.sql" dataSource="slave"/>
    <!--安检抄表，统计时间段内的抄表数量-->
    <sql alias="safeCheckHanderAreaStatistics" path="SafeCheckHanderAreaStatistics.sql"/>
    <!--安检抄表，查询某个时间段内某个小区的抄表详情-->
    <sql alias="safeCheckHanderAreaDetails" path="SafeCheckHanderAreaDetails.sql"/>
    <!--安检抄表 户内总体统计数据6-->
    <sql alias="safeCheckHanderToCount" path="SafeCheckHanderToCount.sql"/>
    <!--手机移动抄表已办-->
    <sql alias="queryCheckHandplanAlreadyList" path="QueryCheckHandplanAlreadyList.sql"/>
    <!--查询计划进度-->
    <sql alias="GetPlanSumInfo" path="GetPlanSumInfo.sql" dataSource="slave"/>


    <!--安检情况汇总（报表1，按月统计指定公司每月安检数和截至到去年年底总用户数所计算的入户率，和统计的全年安检数）-->
    <sql alias="SearchCheckSummaryTab" path="report/SearchCheckSummaryTab.sql"  dataSource="slave"/>
    <sql alias="SearchCheckSummaryTabTS" path="TS/report/SearchCheckSummaryTabTS.sql" dataSource="slave"/>
    <sql alias="SearchLargeAreaCheckSummaryTab" path="report/SearchLargeAreaCheckSummaryTab.sql" dataSource="slave"/>
    <sql alias="SearchLargeAreaCheckSummaryTabTS" path="TS/report/SearchLargeAreaCheckSummaryTabTS.sql" dataSource="slave"/>
    <sql alias="SearchCheckComprehensiveSummaryTabPJ" path="report/SearchCheckComprehensiveSummaryTabPJ.sql" dataSource="slave"/>
    <sql alias="SearchCheckComprehensiveSummaryTabPJTS" path="TS/report/SearchCheckComprehensiveSummaryTabPJTS.sql" dataSource="slave"/>
    <sql alias="SearchCheckComprehensiveSummaryTabZX" path="report/SearchCheckComprehensiveSummaryTabZX.sql" dataSource="slave"/>
    <sql alias="SearchCheckComprehensiveSummaryTabZXTS" path="TS/report/SearchCheckComprehensiveSummaryTabZXTS.sql" dataSource="slave"/>
    <sql alias="SearchComprehensivePlanSummary" path="report/SearchComprehensivePlanSummary.sql"  dataSource="slave"/>
    <sql alias="SearchComprehensivePlanSummaryTS" path="TS/report/SearchComprehensivePlanSummaryTS.sql" dataSource="slave"/>


    <sql alias='getShareCheckPlanTS' path='TS/getShareCheckPlanTS.sql'/>


    <!--超期未安检查询-->
    <sql alias="SearchOverdueNoSafeCheck" path="SearchOverdueNoSafeCheck.sql" dataSource="slave"/>
    <!--根据用户id查询置换记录-->
    <sql alias="SelectReplacementListById" path="SelectReplacementListById.sql"/>
    <!--安检从派单到接单到完成之间时间间隔 安检响应时间-->
    <sql alias="safecheckResponseTimeList" path="TertiarySafecheck/safecheckResponseTimeList.sql"/>
    <!--安检从派单到接单完成时间汇总-->
    <sql alias="safecheckResponseStatistics" path="TertiarySafecheck/safecheckResponseStatistics.sql" dataSource="slave"/>
    <!--安检详情-->
    <sql alias="getAreaSafecheckTreatmentList" path="TertiarySafecheck/getAreaSafecheckTreatmentList.sql" dataSource="slave"/>
    <sql alias="getAreaSafecheckTreatmentInfo" path="TertiarySafecheck/getAreaSafecheckTreatmentInfo.sql" dataSource="slave"/>
    <!--当月是否进行过月度抄表-->
    <sql alias="findHandPlanIsMonth" path="findHandPlanIsMonth.sql"/>
    <!--月度抄表报表-->
    <sql alias="MonthlyMeterReadingStatistics" path="report/MonthlyMeterReadingStatistics.sql" dataSource="slave"/>
    <sql alias="ReadingCheckHandPlanBaseInfo" path="report/ReadingCheckHandPlanBaseInfo.sql" dataSource="slave"/>
    <sql alias="MonthlyMeterReadingStatisticsExcel" path="report/MonthlyMeterReadingStatisticsExcel.sql" dataSource="slave"/>
    <sql alias="MeterReadingRate" path="MeterReadingRate.sql"/>

    <sql alias="ReportSql1" path="report/ReportSql1.sql" dataSource="slave"/>
<!--    用户安检情况查询sql-->
    <sql alias="UserSecuritCheckSituation" path="UserSecuritCheckSituation.sql" dataSource="slave"/>

</cfg>
