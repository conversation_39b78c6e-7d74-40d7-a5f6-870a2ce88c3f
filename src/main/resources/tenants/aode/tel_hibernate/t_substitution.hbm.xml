<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--置换通气单-->
    <class   entity-name="t_substitution" table="t_substitution" >
        <!-- id -->
		<id name="id" type="int">
			<generator class="assigned"></generator>
		</id>
        <!-- 版本号，用于手机端确定工单是否修改过 -->
        <property name="service_version" type="int"></property>
        <!-- 工单id备份，用于android端 -->
        <property name="id_back" type="int"></property>

        <!-- 工单编号 -->
        <property name="f_service_id" type="string" length="255"/>
    	<!-- 工单状态: 撤销: 工单撤销, 空: 其它 -->
        <property name="f_state" type="string" length="255"/>

        <!--工单生成时间 -->
		<property name="f_created_date" type="timestamp" />
		<!--工单类型 -->
		<property name="f_workorder_type" type="string" length='255' />
        <!-- 流程实例id -->
        <property name="f_processid" type="string" length="255"/>
        <!-- 用户姓名/单位名称 -->
		<property name="f_user_name" type="string" length="255"/>
		<!-- 单位名称 -->
		<property name="f_unitname" type="string" length="255"/>
		<!--地址-->
		<property name="f_address" type="string" length="255"/>
		<!--用户类型-->
		<property name="f_user_type" type="string" length="255" />
		<!--用气性质-->
		<property name="f_gasproperties" type="string" length="255" />
		<!--话务id-->
		<property name="f_traffic_id" type="int" />
		<!-- 用户编号  -->
		<property name="f_userinfo_id" type="int"/>
		<!--话务员姓名/受理人-->
		<property name="f_operator_name" type="string" length="255"/>
		<!--联系人-->
		<property name="f_contact_name" type="string" length="255"/>
		<!--联系电话-->
		<property name="f_contact_phone" type="string" length="255"/>
		<!--来电/外拨电话-->
		<property name="f_phone" type="string" length="255"/>
		<!--来电/外拨/咨询/投诉内容-->
		<property name="f_content" type="string" length="255"/>
		<!--来电/外拨/受理日期 == 预约日期 -->
		<property name="f_outphone_date" type="timestamp"/>
		<!-- 录音ID -->
		<property name="f_record_sound" type="string" length="100"/>
		<!--置换通气日期  -->
		<property name="f_repair_date" type="timestamp"/>
		<!--工单预约时间 -->
		<property name="f_yuyue_date" type="string" length='255'/>
		<!--派单人-->
		<property name="f_single_man" type="string" length="255"/>
		<!--接单部门-->
		<property name="f_meetunit" type="string" length="255"/>
		<!--置换通气人-->
		<property name="f_order_man" type="string" length="255"/>
		<!--通气人电话-->
		<property name="f_repairman_phone" type="string" length="255"/>
		<!--话务员-->
        <property name="f_attendant" type="string" length="100"/>
		<!--来电类型-->
		<property name="f_teltype" type="string" length="255"/>
		<!--接收单位  维修员/站点-->
		<property name="f_handlingtype" type="string" length="255"/>
		<!--维修员指导建议-->
		<property name="f_advice" type="string" length="100"/>
		<!--所在位置，维修员/站点/中心-->
		<property name="f_department" type="string" length="255"/>
		<!--接单日期 == 派单日期-->
		<property name="f_order_date" type="timestamp"/>
		<!--是否回访-->
		<property name="f_revisit" type="string" length="255"/>
		<!--回访员-->
		<property name="f_visit_member" type="string"/>
		<!--回访日期-->
		<property name="f_revisit_date" type="timestamp"/>
		<!--用户意见-->
		<property name="f_user_opinion" type="string" length="255"/>
		<!--服务评价-->
		<property name="f_appraise" type="string" length="255"/>
		<!--备注-->
		<property name="f_remarks" type="string" length="255"/>
		<!-- 营业网点 -->
		<property name="f_outlets" type="string" length="50"/>
		<!-- 组织机构 -->
		<property name="f_orgstr" type="string" length="200"/>
		<!-- 分公司 -->
		<property name="f_filiale" type="string" length="200"/>
		<!-- 分公司id -->
		<property name="f_filiale_id" type="string" length="50"/>
		<!-- 组织(分公司)id -->
		<property name="f_orgid" type="string" length="50"/>
		<!-- 组织(分公司)操作人的公司 -->
		<property name="f_orgname" type="string" length="50"/>
		<!--操作人的部门 -->
		<property name="f_depname" type="string" length="50"/>
		<!-- 操作人的部门id -->
		<property name="f_depid" type="string" length="50"/>
		<!--操作人 -->
		<property name="f_operator" type="string" length="50"/>
		<!-- 操作人的id -->
		<property name="f_operatorid" type="string" length="50"/>
		<!--公司机构ids -->
		<property name="f_filialeids" type="string" length="200" />
		<!-- 该工单是否退回: null 没退回  true 已退回 -->
        <property name="f_backsuccess" type="string" length="10"/>
		<!-- 是否完成: 未完成/已完成/待完成 -->
        <property name="f_finish" type="string" length="10"/>
		<!-- 是否检查项异常 -->
		<property name="f_is_abnormal" type="string" length="10"/>
		<!-- 故障明细,json格式:{故障类型:{f_equipment:设备品牌,f_failure:['故障']}} -->
		<property name="failure" type="string" length="2000"/>
		<!-- 报修单表对应一次活动一对多 -->
		<set name="serviceacitivity" cascade="all-delete-orphan" inverse="true">
			<key column="serviceid" on-delete="cascade" />
			<one-to-many entity-name="t_service_acitivity" not-found="exception" />
		</set>
        <!-- 工单来源 -->
        <property name="f_source" type="string" length="200"/>
   <!-- 置换通气特殊字段 -->
        <!-- 地址串  节点id -->
        <property name="f_addressid" type="int" />
        <!-- 地址串明细  json格式:[{name:2号小区,type:小区},{name:1单元,type:单元}] -->
        <property name="f_addressjson" type="string" length="1000"/>
        <!-- 地址  小区ID -->
        <property name="f_districtname_id" type="string" length="50"/>
        <!-- 地址  门牌号ID -->
        <property name="f_room_id" type="string" length="50"/>
		<!-- 安检转维修隐患项  参考维修配置文件 -->
		<property name="f_repairitems" type="string" length="2000"/>
	<!-- 手机端特殊字段 -->
		<!-- 是否上传成功: 成功/失败 -->
        <property name="f_success" type="string" length="10"/>
		<!-- 工单状态:  null/未完成/已完成/-->
		<property name="f_orderstatus" type="string" length="10"/>
        <!-- 是否延期申请提交成功: 成功/失败 -->
        <property name="f_delaysuccess" type="string" length="10"/>
        <!-- 工单json串形式，手机端工单整体内容按json串存放 -->
        <property name="f_json" type="string"/>
		<!-- 催单标准位是否被用戶催单：false/true(不催单、催单) -->
        <property name="f_remindersign" type="string" length="10"/>
		<!-- 催单内容 -->
		<property name="f_reminderdata" type="string" length="2000"/>
		<!--是否延期-->
		<property name="f_isdelay" type="string" length="100"/>
	<!-- 安检转维修特殊字段 -->
		<property name="f_paperid" type="string" length="100"/>
		<!-- 工单申请id-->
		<property name="f_orderservice_id" type="string" length="50"/>
	</class>
</hibernate-mapping>
