<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--管道维修-->
    <class   entity-name="t_pipeservice" table="t_pipeservice" >
        <!-- id -->
        <id name="id" type="int" column="id">
			<generator class="native"></generator>
        </id>
		<!--维修单编号 -->
		<property name="f_service_id" type="string" length='20'/>
		<!--维修员-->
		<property name="f_maintain_member"  type="string" length="20"/>
		<!-- 引入方式 -->
		<property name="f_import_mode"  type="string" length="50"/>
		<!-- 处理结果 -->
		<property name="f_handl_result"  type="string" length="20"/>
		<!-- 位置-->
		<property name="f_location"  type="string" length="50"/>
		<!-- 气密性检查人 -->
		<property name="f_checkman"  type="string" length="20"/>
		<!--备注-->
		<property name="f_remarks" type="string" length="100"/>
	</class>
</hibernate-mapping>
