<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	<!-- 话务员状态变更记录表 -->
	<class entity-name="t_attendant_status" table="t_attendant_status">
		<id name="id" type="string">
			<generator class="assigned"></generator>
		</id>
		<!--话务员姓名-->
		<property name="name" type="string"/>
		<!-- 话务员模块  -->
		<property name="module" type="string"/>
		<!-- 话务员通道  -->
		<property name="channel" type="string"/>
		<!-- 是否voip, 是/否 -->
		<property name="isvoip" type="string"/>
		<!--状态 登入 登出 置忙-->
		<property name="operation" type="string"/>
		<!-- 操作发生时间  -->
		<property name="opstart" type="string"/>
		<!-- 操作结束时间  -->
		<property name="opend" type="string"/>
		<!-- 操作时长, 按秒计 -->
		<property name="opspan" type="int"/>
		<!-- 状态对应的话务id -->
		<property name="callid" type="string"/>
	</class>
</hibernate-mapping>