<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--一次维修信息-->
    <class entity-name="t_delayapply" table="t_delayapply" >
        <!-- id -->
		  <id name="id" type="int">
			<generator class="assigned"></generator>
		  </id>
		
		<!--工单编号 -->
		<property name="serviceid" type="int" length="100"/>
		<!-- 用户微信的openid -->
		<property name="f_open_id" type="string" length='100'/>
		<!-- 活动类型 -->
		<property name="f_service_acitivity_type" type="string" length="20"/>
        <!--活动编号 -->
		<property name="f_service_acitivity_id" type="string" length='20'/>
		<!--申请时间 -->
		<property name="f_date" type="timestamp" />
		<!--申请延期时间 -->
		<property name="f_dalay_date" type="timestamp" />
		<!--申请人员 -->
		<property name="f_name" type="string" length="50"/>
		<!--申请原因 -->
		<property name="f_reason" type="string" length="100"/>
	</class>
</hibernate-mapping>
