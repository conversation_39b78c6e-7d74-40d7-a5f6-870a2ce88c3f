<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--处理完成信息-->
    <class   entity-name="t_phonefinsh" table="t_phonefinsh" >
        <!-- id -->
		<id name="id" type="int">
	        <generator class="native"></generator>
		  </id>
        <!--工单编号 -->
		<property name="serviceid" type="int" length="100"/>
        <!--活动编号 -->
		<property name="f_service_acitivity_id" type="string" length='20'/>
		<!-- 活动类型 -->
		<property name="f_service_acitivity_type" type="string" length="20"/>
		<!-- 用户编号  -->
		<property name="f_userinfo_id" type="int" />
		<!-- 用户姓名/单位名称 -->
		<property name="f_user_name" type="string" length="255"/>
		<!--地址-->
		<property name="f_address" type="string" length="255"/>
		<!--用户类型-->
		<property name="f_user_type" type="string" length="255" />
		<!--话务id-->
		<property name="f_traffic_id" type="int" />
		<!--电话处理人-->
		<property name="f_finsh_name" type="string" length="20"/>
		<!--联系人-->
		<property name="f_contact_name" type="string" length="255"/>
		<!--联系电话-->
		<property name="f_contact_phone" type="string" length="255"/>
		<!--来电/外拨电话-->
		<property name="f_phone" type="string" length="255"/>
		<!--来电/外拨/咨询/投诉内容-->
		<property name="f_content" type="string" length="255"/>
		<!--处理方式-->
		<property name="f_dealtype" type="string" length="255"/>
		<!--备注-->
		<property name="f_remarks" type="string" length="255"/>
		<!--处理日期-->
		<property name="f_date" type="timestamp" length="20"/>
   </class>
</hibernate-mapping>
