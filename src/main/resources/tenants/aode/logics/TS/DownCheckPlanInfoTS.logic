log.info(msg:"========下载计划=={data}======"),
log.info(msg:"context-- 下载计划 -- {context}"),
result = "",
// 从服务器端获取共享计划信息
info = {
    id:data.id
},
restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/GetCheckPlanIntoToServerTS", data: info.toString()},
ret = restTools.action(param:restJson),
log.info(msg:"==========访问服务后的返回信息============{ret}"),
// 将抄表单信息保存到本地android
log.info(msg:"{ret.data}"),
count = 0,
ret.code == 200 : (
	ret.data.each(
		params = entity.action(json:{method: "save", entity: "t_check_plan_ts", data: row}),
		log.info(msg:"==============保存数据完成后返回===========>>{params}")
	),
	result = {
		state: 0,
		msg: "OK",
		data: ret.data
	}
),
(
	result = {
		state: 1,
		msg: "暂无可抄表记录",
		data: ret.data
	}
),
result
