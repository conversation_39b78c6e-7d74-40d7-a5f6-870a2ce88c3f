//下班签到
//检查是否已经签到
data.f_knockoff_time = dateTools.getNow2(),
signRecord = sql.query("获取签到状态",data),
log.info(signRecord.toString()),
length = signRecord.length(),

//如果已下班签到，返回已经签到信息和下班签到时间
length == 1:
(
	sr = signRecord[0],
	ko = ""{sr.f_knockoff_time},
	log.info(ko),
	ko == "null":
	(
		//没有下班签到
		sr.f_knockoff_time = data.f_knockoff_time,
		entity.partialSave("t_user_signin", sr),
		{msg: "签到成功! 下班签到时间："{data.f_knockoff_time}, time: "{data.f_knockoff_time}"}
	),
	(
		{msg: "签到成功! 下班签到时间："{data.f_knockoff_time}, time: "{data.f_knockoff_time}"}
	)
),
(
	length == 0:
	(
		entity.partialSave("t_user_signin", data),
		{msg: "签到成功! 下班签到时间："{data.f_knockoff_time}, time: "{data.f_knockoff_time}"}
	),
	(
		throw {status: 500, msg: "查询签到信息出错"}
	)
)
