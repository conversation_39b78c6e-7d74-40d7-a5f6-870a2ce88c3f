//用户表 每个用户都有一个安检负责人，每个用户都有最后安检日期，每个用户都有一个安检周期
userfiles = sql.query("FindCheckerAnnualPlan", data),
lengths = userfiles.length(),
lengths < 1 :(
	throw {status: 504, msg: "查询出错"}
),null,
//给每个人制定安检计划  插入计划表
userfiles.each(
    year = subString.getYear(row.plan_year_month),
    month = subString.getMonth(row.plan_year_month),
    plan_year = year,
    month == 1 :(
    	month=12,
    	year=year-1
    ),
    month != 1 :(
    	month = month -1
    ),null,
    log.info(year),
    log.info(month),
    planname = subString.sub(row.plan_year_month,4,7),
	value = entity.partialSave("t_check_plan",{
        f_plan_name: "{row.f_checker}_{year}-{month}",
        f_plan_year: year,
        f_plan_month: month,
        f_checker: row.f_checker,
        f_checker_id: row.f_checker_id,
        f_subcompany: data.f_subcompany,
        f_plan_type: "自动计划",
        f_issued: "否"
    }),
	check = sql.query("FindAnnualPlanItem",{
	    f_checker: row.f_checker,
	    f_subcompany: data.f_subcompany,
	    plan_year_month: row.plan_year_month
    }),
	check.each(
		entity.partialSave("t_check_plan_item",{
            f_userfile_id: row.id,
            f_userinfoid: row.f_userinfoid,
            f_user_type: row.f_user_type,
            f_user_name: row.f_user_name,
            f_street: row.f_street,
            f_residential_area: row.f_residential_area,
            f_building: row.f_building,
            f_unit: row.f_unit,
            f_floor: row.f_floor,
            f_room: row.f_room,
            f_address: row.f_address,
            f_user_phone: row.f_user_phone,
            f_card_id: row.f_card_id,
            f_subcompany: data.f_subcompany,
            f_state: "未检",
            f_plan_id: value,
            f_approved: "未审核",
            f_repair_approved: "未审核",
            f_upload_state: "未传",
            f_last_check_state: row.f_last_check_state,
            f_last_check_date: row.f_last_check_date
        })
	)
)
