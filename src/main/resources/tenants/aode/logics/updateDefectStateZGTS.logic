log.info("隐患整改：{data}"),
log.info("进入updateDefectStateZG.logic ===> {data}"),
data.f_create_time=dateTools.getNow2(),
sql.execSQL("v3_safecheck_sql", "
	UPDATE t_devices_items_ts SET f_is_repaired = '已转维修'
	WHERE id IN {data.itemsId}
"),
sql.execSQL("v3_safecheck_sql", "
	UPDATE t_check_paper_ts
	    SET
	        f_SafeToRepair_id='{data.serviceid}',
	        f_repaired='已转维修'
    where id='{data.paperid}'
"),
checkToRepair = {
    f_check_paper_id: data.paperid,
    f_service_id: data.serviceid,
    f_create_date: dateTools.getNow2()
},
// 保存安检转维修中间表记录
entity.partialSave("t_check_turn_repair", checkToRepair),


{code:200}
