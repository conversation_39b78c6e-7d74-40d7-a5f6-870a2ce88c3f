log.info("置换组装数据"),
services = sql.querySQL("ZhiHuanServiceWorkLogic_sql","
    select * from t_servicework WITH(nolock)
        where f_is_userfilesadd = '未上传' and f_workorder_type = '置换通气单' and f_is_abnormal is null
"),
services.each(
    service = row,
    repairs =  sql.querySQL("ZhiHuanServiceWorkLogic_sql","
        select * from t_servicerepair WITH(nolock)  where serviceid = '{service.id}'
    "),
    service.servicerepair = jsonTools.getArray(),
    repairs.each(
        repair = row,
        details = sql.querySQL("ZhiHuanServiceWorkLogic_sql","
            select * from t_details WITH(nolock) where  f_service_id = '{repair.id}'
        "),
        repair.details = details,
        service.servicerepair.put(repair)
    ),
    baseuserinfos = sql.querySQL("ZhiHuanServiceWorkLogic_sql","
        select * from t_baseuserinfo WITH(nolock) where id = '{service.id}'
    "),
    baseuserinfos.length() > 0 :(
        service.baseuserinfo = baseuserinfos[0]
    ),(
        service.baseuserinfo = {}
    ),
    repairimgs =  sql.querySQL("ZhiHuanServiceWorkLogic_sql","
       select * from t_repairimg WITH(nolock) where serviceid = '{service.id}'
    "),
    service.imgs = repairimgs
),
{code:200,data:services}
