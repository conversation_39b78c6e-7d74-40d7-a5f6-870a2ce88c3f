resid=sql.querySQL("v3_safecheck_sql", "select f_state,f_last_check_state,f_check_version,id,f_addressid,service_id from t_check_plan_item with (nolock) where id='{data.paper.f_check_item_id}'"),
resid.length()>0:(
    resid[0].f_state =="已检"&& resid[0].f_last_check_state =="入户":(
        return {code:200,data:{msg:"已入户，请勿重复上传！"}}
    ),null
),null,
data.paper.f_state = "已检",
data.paper.f_approved = "未审核",
data.paper.f_upload_date = dateTools.getNow2(),
htts = "http:/",
logic.run("Changelog",data),
//查询用于地址中的到访不遇次数
ver=1,
data.paper.f_entry_status=="到访不遇":(
    resid[0].f_check_version==null || resid[0].f_check_version=="":(
        data.paper.f_check_version=1
    ),(
        data.paper.f_check_version=resid[0].f_check_version+1
    ),
    ver=data.paper.f_check_version%3
),(
    data.paper.f_check_version=0,
    ver=0,
    log.info("----------入户修改f_check_version:{data.paper.f_check_version}-----------")
),
data.paper.f_comments="",
data.paper.f_subcompany="是",
data.paper.f_devices.each(
    row.f_device_type == "燃气表" || row.f_device_type == "安检详情" :(
        row.f_items.each(
            row.f_item_name == $差值备注$ && row.f_item_lists.length() > 0:(data.paper.f_difference_remarks = row.f_item_lists[0].f_item_value),null,
            row.f_item_name == "是否为壁挂炉用户" && row.f_item_lists.length() > 0:(data.paper.f_bigualu = row.f_item_lists[0].f_item_value),null,
            row.f_item_name == "是否为热水器用户" && row.f_item_lists.length() > 0:(data.paper.f_reshuiqi = row.f_item_lists[0].f_item_value),null,
            row.f_item_name == "累购气量" && row.f_item_lists.length() > 0:(data.paper.f_leigou = row.f_item_lists[0].f_item_value),null,
            row.f_item_name == "户累购气量" && row.f_item_lists.length() > 0:(data.paper.f_lasthisgas = row.f_item_lists[0].f_item_value),null,
            row.f_item_name == "已用气量" && row.f_item_lists.length() > 0:(data.paper.f_yiyong = row.f_item_lists[0].f_item_value),null,
            row.f_item_name == "剩余气量" && row.f_item_lists.length() > 0:(data.paper.f_shengyu = row.f_item_lists[0].f_item_value),null
        )
    ),null
),
log.info("----------data:{data.paper}-----------"),
jsoncompare.empty(data.paper),
f_paper_id = entity.partialSave("t_check_paper", data.paper),
log.info("上传数据paper的f_paper_id----------------------》》{f_paper_id}--ver:{ver}---data.paper.f_check_version:{data.paper.f_check_version}---{resid[0].f_addressid}"),
sql.execSQL("v3_safecheck_sql",  "
   update t_check_plan_item set f_state='已检',f_upload_state='已传',f_complete = '已完成',f_last_check_state='{data.paper.f_entry_status}',f_last_check_date='{data.paper.f_upload_date}',
   version =version+1,f_check_version={data.paper.f_check_version} where id = '{data.paper.f_check_item_id}'
"),
// 只有入户才更新档案中 壁挂、热水器状态
userUpStr = "f_last_check_date = '{data.paper.f_upload_date}',f_last_check_state = '{data.paper.f_entry_status}',f_last_check_result = '{data.paper.f_defect_content}'",
data.paper.f_entry_status=="入户":(
    userUpStr = "{userUpStr},f_has_stove='{data.paper.f_bigualu}',f_has_heater='{data.paper.f_reshuiqi}'"
),null,
data.paper.f_safecheck_type=="年度普检":(
log.info("----------data:进入年度普检-----------"),
    ver==0:(
        sql.execSQL("v3_safecheck_sql", "update t_userinfo set {userUpStr} where f_userinfo_id = '{data.paper.f_userinfoid}'"),
        sql.execSQL("v3_safecheck_sql", "update t_user_address set f_plan_id=null,f_check_version={data.paper.f_check_version}, f_last_check_date = '{data.paper.f_upload_date}', f_last_check_state = '{data.paper.f_entry_status}', f_last_check_result='{data.paper.f_defect_content}',f_last_checker='{data.paper.f_checker_name}' where id = '{resid[0].f_addressid}'")
    ),(
      sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version={data.paper.f_check_version}  where id = '{resid[0].f_addressid}'")
    )
),(
log.info("----------data:未进入年度普检-----------"),
    ver==0:(
        sql.execSQL("v3_safecheck_sql", "update t_userinfo set {userUpStr} where f_userinfo_id = '{data.paper.f_userinfoid}'"),
        sql.execSQL("v3_safecheck_sql", "update t_user_address set f_last_check_date = '{data.paper.f_upload_date}', f_last_check_state = '{data.paper.f_entry_status}', f_last_check_result='{data.paper.f_defect_content}',f_last_checker='{data.paper.f_checker_name}' where id = '{resid[0].f_addressid}'")
    ),null
),
//到访不遇三次 算此单完成 修改地址状态
//安检时回写档案信息： 用户姓名 租户姓名 电话
data.paper.has("f_user_phone2"):(
    sql.execSQL("v3_safecheck_sql", "
        update t_userinfo set f_rent_phone='{data.paper.f_user_phone2}' where f_userinfo_id='{data.paper.f_userinfoid}'
    ")
),null,
paper_id = jsonTools.convertToJson(f_paper_id),
log.info("paper_id===>{paper_id.id}"),

// 保存联系电话修改记录，并修改档案的用户电话
data.paper.has("f_user_phone") && data.paper.f_user_phone != "" && data.paper.f_user_phone != null: (
    log.info("判断是否修改了用户电话，保存联系电话修改记录"),
    paper_params = {
            data:{
                f_userinfoid:data.paper.f_userinfoid,
                f_checker_name:data.paper.f_checker_name,
                f_user_name:data.paper.f_user_name,
                f_user_phone: data.paper.f_user_phone,
                f_checker_id: data.paper.f_checker_id
            }
        },
    safe_queue_data = {
        f_task_description: "安检单回写档案",
        f_params: paper_params.toString().replaceAll("'",""),
        f_target_url: "{htts}/**************:8300/rs/logic/safe_saveRecordChangeInfo",
        f_request_methed: "POST",
        f_task_creater: data.paper.f_checker_name,
        f_task_creater_id: data.paper.f_checker_id
    },
    entity.partialSave("t_safe_queue_task", safe_queue_data)
//    rcode = restTools2.post("/rs/logic/safe_saveRecordChangeInfo",{data:data.paper}),
//    log.info("返回修改用户电话返回值,{rcode}"),
//    rcodeJson = jsonTools.convertToJson(rcode),
//    rcodeJson.code = 200  :(
//        sql.execSQL("v3_safecheck_sql", "update t_userinfo set f_user_phone = '{data.paper.f_user_phone}' where f_userinfo_id = '{data.paper.f_userinfoid}'")
//    ),null
),null,

resid[0].has("service_id"):(
    resid[0].service_id!=null && resid[0].service_id!="":(
        params={
            service_id:resid[0].service_id,
            name:data.paper.f_checker_name,
            ename:data.paper.f_checker_id,
            id: paper_id.id,
            f_date_come:data.paper.f_onsite_time,
            f_date_leave:data.paper.f_offsite_time,
            f_date:data.paper.f_upload_date
        },
        safe_queue_data = {
            f_task_description: "安检单提交预约单处理后续逻辑",
            f_params: params.toString().replaceAll("'",""),
            f_target_url: "{htts}/**************:8531/telephone/rs/logic/YuYueFinish",
            f_request_methed: "POST",
            f_task_creater: data.paper.f_checker_name,
            f_task_creater_id: data.paper.f_checker_id
        },
        entity.partialSave("t_safe_queue_task", safe_queue_data)
//        restTools2.post("{htts}/**************:8531/telephone/rs/logic/YuYueFinish",params)
    ),null
),null,

results={
    papers:"用不到",
    id:paper_id.id,
    f_check_version:data.paper.f_check_version
},
{code:200,data:results}
