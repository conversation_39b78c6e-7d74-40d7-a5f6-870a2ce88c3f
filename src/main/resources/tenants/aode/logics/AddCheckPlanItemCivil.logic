//新增安检计划项 并添加活动
//data:{
//      {                                                                                   }
//      f_plan_id:'',
//      f_operator:'',
//       f_filialeid:'',
//      f_create_time:'',
//      condition:'sql条件', 查询条件
//      switchCheckAll:true/false是否有选择功能
//      checkAll:true/false, 是否选中 ‘全选按钮’
//      param[...], 选中的字段的值
//      columnName 需要做查询的字段名
//}
//
log.info("---------data:{data}"),
data.f_remark != null :(
	f_remark = data.f_remark
),(
	f_remark = ""
),
tableName = "##tempzx_data_{commonTools.getUUID(true)}",
sqlstr = logic.run("ConditionalGeneration",data),
data.f_create_time=dateTools.getNow2(),
newid=commonTools.getUUID(),
log.info("当前时间{data.f_create_time}"),
// 查询当前计划的计划数
plancon = sql.querySQL("查询当前计划的计划数", "
    select count(id) con from t_check_plan_item  WITH(nolock) where f_plan_id =  '{data.f_plan_id}'
"),
// 查询添加到计划的用户数
arrs1=sql.querySQL("查询添加到计划的用户数","
    select count(1) con
    from t_user_address ua WITH(nolock)
    join t_userinfo ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles uf WITH(nolock) on uf.f_userinfo_id = ui.f_userinfo_id
    left join t_check_plan_item tcpi on tcpi.f_userinfoid = ui.f_userinfo_id and tcpi.f_plan_id = '{data.f_plan_id}'
    where {sqlstr} and (uf.f_table_state='正常' or uf.f_table_state='停用')
    and tcpi.id is null
"),
in_plan=sql.querySQL("v3_safecheck_sql", "
    select ui.f_userinfo_code
    from t_user_address ua WITH(nolock)
    join t_userinfo ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles uf WITH(nolock) on uf.f_userinfo_id = ui.f_userinfo_id
    left join t_check_plan_item tcpi on tcpi.f_userinfoid = ui.f_userinfo_id and tcpi.f_plan_id = '{data.f_plan_id}'
    where {sqlstr} and (uf.f_table_state='正常' or uf.f_table_state='停用')
    and tcpi.id is not null
    GROUP BY ui.f_userinfo_code
"),
// 单条计划数必须小于5000条
sqlCount = 0,
arrs1.length() > 0: (
    sqlCount = arrs1[0].con
),null,
// 当前计划的原计划数
thisPlanCount = 0,
plancon.length() > 0: (
    thisPlanCount = plancon[0].con
),null,

commonTools.add(sqlCount, thisPlanCount) < 5000: (
    service_id = null,
    data.has("service_id"):(
        service_id = data.service_id
    ),null,
    sql.execSQL("create_plan_item_temp","
        SELECT temp.* INTO {tableName}
        FROM (
            SELECT
            newId() id,
            ui.f_userinfo_code,
            ui.f_userinfo_id,
            ui.f_idnumber,
            ua.f_area,
            ua.f_slice_area,
            ua.f_street,
            ua.f_residential_area,
            ua.f_building,
            ua.f_unit,
            ua.f_floor,
            ua.f_room,
            {data.f_address != null:"'{data.f_address}' f_address","ua.f_address"},
            '未检' f_state,
            '未传' f_upload_state,
            '{data.f_plan_id}' f_plan_id,
            ui.f_user_name,
           {data.f_user_phone != null:"'{data.f_user_phone}' f_user_phone","ui.f_user_phone"},
            uf.f_user_type,
            '未审核' f_approved,
            '有计划安检' f_no_checkplan,
            ua.f_last_check_state,
            ua.f_last_check_date,
            '未审核' f_repair_approved,
            '{data.f_filialeid}' f_filialeid,
            '{data.f_create_time}' f_create_time,
            ui.version,
            ua.f_last_check_result,
            ui.f_comments,
            ua.id f_addressid,
            '{data.f_safecheck_type}' f_safecheck_type,
            ua.f_year_plan_id,
             '{f_remark}' f_remark,
             CONVERT(VARCHAR(32),ui.f_open_date,121) f_open_date,
             0 f_check_version,
             CONVERT(varchar(100),va.f_start_date,23) f_insurance_gas_date,
             {service_id != null && service_id != "":"'{service_id}' service_id","null service_id"},
             't_userinfo' f_user_source,
             uf.f_meternumber f_meter_no
            FROM
               t_user_address ua WITH(nolock)
               left join t_userinfo ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
               left join t_value_add va WITH(nolock) on ui.f_userinfo_id = va.f_userinfo_id and f_value_name='燃气险'
               left join t_userfiles uf WITH(nolock) on uf.f_userinfo_id = ui.f_userinfo_id
               left join t_check_plan_item tcpi on tcpi.f_userinfoid = ui.f_userinfo_id and tcpi.f_plan_id = '{data.f_plan_id}'
                WHERE {sqlstr} and (uf.f_table_state='正常' or uf.f_table_state='停用')
                and tcpi.id is null
        ) AS temp
    "),
    // 新增item数据
    sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_plan_item (
        id,
        f_userinfo_code,
        f_userinfoid,
        f_idnumber,
        f_area,
        f_slice_area,
        f_street,
        f_residential_area,
        f_building,
        f_unit,
        f_floor,
        f_room,
        f_address,
        f_state,
        f_upload_state,
        f_plan_id,
        f_user_name,
        f_user_phone,
        f_user_type,
        f_approved,
        f_no_checkplan,
        f_last_check_state,
        f_last_check_date,
        f_repair_approved,
        f_filialeid,
        f_create_time,
        version,
        f_last_check_result,
        f_comments,
        f_addressid,
        f_safecheck_type,
        f_year_plan_id,
        f_remark,
        f_open_date,
        f_check_version,
        f_insurance_gas_date,
        service_id,
        f_user_source,
        f_meter_no
        ) SELECT
        *
        FROM {tableName}
    "),
    sql.execSQL("delete_tempzx_data","
        drop table {tableName}
    "),
    {code:200,resultno:arrs1,in_plan:in_plan}
),(
    {code:501,resultno:arrs1,msg: "派发失败，每个计划下最多5000户，您本次添加了{sqlCount}户，请分为多个计划！"}
)



