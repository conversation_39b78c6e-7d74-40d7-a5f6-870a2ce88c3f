<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--对接奥德远传系统基础信息表-->
    <class entity-name="t_remote_system_baseinfo" table="t_remote_system_baseinfo">
        <!--奥德远传系统对接用户编号为唯一标识-->
        <id name="f_userinfo_code" type="string">
            <generator class="assigned"/>
        </id>
        <!-- 厂家 -->
        <property name="cj" type="string"/>
        <!--远传站点编号-->
        <property name="sta_id" type="string"/>
        <!-- 计量类型-->
        <property name="lx" type="string"/>
        <!-- 型号-->
        <property name="xh" type="string"/>
        <!--出厂编号 -->
        <property name="sn" type="string"/>
        <!-- 口径 -->
        <property name="kj" type="string"/>
        <!-- 铅封号 -->
        <property name="qf" type="string"/>
        <!-- 计量精度 -->
        <property name="jd" type="string"/>
        <!--量程下限-->
        <property name="min" type="string"/>
        <!--量程上限-->
        <property name="max" type="string"/>
        <!--使用日期-->
        <property name="usetime" type="string"/>
        <!-- 计量方式 -->
        <property name="jlfs" type="string"/>
        <!-- 创建时间 -->
        <property name="f_create_time" type="string"/>
        <!-- 修改时间 -->
        <property name="f_update_time" type="string"/>
    </class>
</hibernate-mapping>
