<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--安检单对应的计数信息-->
    <class entity-name="t_paper_hiddens" table="t_paper_hiddens">
        <id name="id" type="int" column="id">
            <generator class="identity">
                <param name="identity">person_id_sequence</param>
            </generator>
        </id>
        <!-- 设备类型-->
        <property name="f_device_type" type="string"/>
        <!-- 关联-->
        <property name="f_paper_id" type="string"/>
        <!--检查项信息名称 -->
        <property name="f_item_name" type="string"/>
        <!--检查项信息值 -->
        <property name="f_item_value" type="string"/>
        <!--检查项信息值类型-->
        <property name="type" type="string"/>
        <!-- 是否是隐患-->
        <property name="f_is_defect" type="string"/>
        <!-- 原照片1  -->
        <property name="f_path" type="string"/>
        <!-- 原照片2  -->
        <property name="f_second_path" type="string"/>
        <!-- 隐患级别-->
        <property name="f_defect_level" type="string"/>
        <!--隐患的处理方式 【现场处理、用户自行处理、需转维修处理】-->
        <property name="f_processing" type="string"/>
        <!-- 是否进行现场处理  【是、否】 -->
        <property name="f_live_dispose" type="string"/>
        <!-- 是否已经转维修处理过了 【已转维修、需转维修、不转维修、维修已处理】 -->
        <property name="f_is_repaired" type="string"/>
        <!-- 维修后图片 -->
        <property name="f_repair_path" type="string"/>
        <!-- 维修人 -->
        <property name="f_repairman" type="string"/>
        <!-- 维修时间 -->
        <property name="f_repair_date" type="string"/>
        <!-- 是否用户整改  【是、否】 -->
        <property name="f_user_changes" type="string"/>
        <!--情况说明-->
        <property name="f_item_remakes" type="string"/>
        <!-- 一级隐患是否消除-->
        <property name="f_is_eliminate" type="string"/>
        <!-- 创建时间 -->
        <property name="f_create_time" type="string"/>
    </class>
</hibernate-mapping>
