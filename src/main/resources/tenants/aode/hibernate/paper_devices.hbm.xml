<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--安检单对应的计数信息-->
    <class entity-name="t_paper_devices" table="t_paper_devices">
        <id name="id" type="int" column="id">
            <generator class="identity">
                <param name="identity">person_id_sequence</param>
            </generator>
        </id>
        <!-- 设备类型-->
        <property name="f_device_type" type="string"/>
        <!-- 关联-->
        <property name="f_paper_id" type="string"/>
        <!-- 安检项信息 -->
        <set name="f_items" cascade="delete">
            <key column="f_device_id" on-delete="noaction"/>
            <one-to-many entity-name="t_devices_items" not-found="exception"/>
        </set>
    </class>
</hibernate-mapping>
