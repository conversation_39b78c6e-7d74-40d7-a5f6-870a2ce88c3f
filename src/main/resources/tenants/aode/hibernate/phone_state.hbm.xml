<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--手机状态-->
    <class entity-name="t_phone_state" table="t_phone_state">
        <id name="id" type="int">
            <generator class="identity"/>
        </id>
        <!-- 状态类型 最新 分 天 -->
        <property name="f_period" type="string"/>
        <!-- 时间戳 -->
        <property name="f_datetime" type="string"/>
        <!-- 真实提取时间 -->
        <property name="f_realtime" type="string"/>
        <!-- 手机使用者 -->
        <property name="f_user_name" type="string"/>
        <!-- 手机使用者id-->
        <property name="f_user_id" type="string"/>
        <!-- 手机串码 -->
        <property name="f_sn" type="string"/>
        <!-- 经度 至少6位精度-->
        <property name="f_longitude" type="string"/>
        <!-- 纬度 至少6位精度-->
        <property name="f_latitude" type="string"/>
        <!-- 海拔 -->
        <property name="f_altitude" type="string"/>
        <!-- 提供者 -->
        <property name="f_provider" type="string"/>
        <!-- 电量 -->
        <property name="f_battery_level" type="int"/>
        <!-- 信号强度 -->
        <property name="f_signal_strength" type="int"/>
        <!-- 内置存储剩余空间  单位M -->
        <property name="f_internal_quote" type="int"/>
        <!-- 外部存储剩余空间  单位M -->
        <property name="f_external_quote" type="int"/>
        <!-- 流量 K -->
        <property name="f_traffic" type="int"/>
        <!-- 子公司 -->
        <property name="f_subcompany" type="string"/>
        <!-- 备注 -->
        <property name="f_remark" type="string"/>
    </class>
</hibernate-mapping>
