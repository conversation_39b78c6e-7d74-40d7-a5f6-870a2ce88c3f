<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--安检计划-->
    <class entity-name="t_check_plan_ts" table="t_check_plan_ts">
        <id name="id" type="string">
            <generator class="assigned"/>
        </id>
        <!-- 计划名称 -->
        <property name="f_plan_name" type="string"/>
        <!-- 计划年份-->
        <property name="f_plan_year" type="int"/>
        <!-- 计划月份   0 为全年计划-->
        <property name="f_plan_month" type="int"/>
        <!-- 安检类型-->
        <property name="f_safecheck_type" type="string"/>
        <!-- 计划接收安检员 -->
        <property name="f_checker" type="string"/>
        <!-- 计划接收安检员 -->
        <property name="f_checker_id" type="string"/>
        <!-- 计划下发人 管理员 或 安检员 -->
        <property name="f_planner" type="string"/>
        <!-- 计划下发人 管理员 或 安检员 -->
        <property name="f_planner_id" type="string"/>
        <!-- 计划类型 民用、非民用、预约计划、抽样计划-->
        <property name="f_plan_type" type="string"/>
        <!-- 下发时间 -->
        <property name="f_issue_time" type="string"/>
        <!-- 组织机构 -->
        <property name="f_orgstr" type="string" length="200"/>
        <!--公司机构ids -->
        <property name="f_filialeids" type="string" length="200"/>

        <!--所属公司 -->
        <property name="f_filialeid" type="string"/>

        <!-- 所属子公司 -->
        <property name="f_subcompany" type="string"/>
        <!-- 安技办 -->
        <property name="f_office" type="string"/>
        <!-- 安检所 -->
        <property name="f_station" type="string"/>
        <!-- 是否下发 -->
        <property name="f_issued" type="string"/>
        <!-- 备注 -->
        <property name="f_remark" type="string"/>
        <!-- 生成人员 -->
        <property name="f_create_operator" type="string"/>
        <!-- 下发人员 -->
        <property name="f_send_operator" type="string"/>
        <!-- 生成时间 -->
        <property name="f_create_time" type="string"/>
        <!-- 有效起始时间 -->
        <property name="f_effective_start_time" type="string"/>
        <!-- 有效结束时间 -->
        <property name="f_effective_end_time" type="string"/>
        <!-- 计划项 -->
        <set name="f_items" cascade="delete">
            <key column="f_plan_id" on-delete="noaction"/>
            <one-to-many entity-name="t_check_plan_item_ts" not-found="exception"/>
        </set>
    </class>
</hibernate-mapping>
