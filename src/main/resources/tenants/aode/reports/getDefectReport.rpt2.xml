<report>
  <sql name="a" sql="report_getOrg" param="{startDate:startDate,endDate:endDate,f_filialeid:f_filialeid,condition:condition}" />
  <sql name="b" sql="report_getAllDefectCount" param="{startDate:startDate,endDate:endDate,f_filialeid:f_filialeid,condition:condition}" />
  <sql name="c" sql="report_getAllDefectAll" param="{startDate:startDate,endDate:endDate,f_filialeid:f_filialeid,condition:condition}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="200" startx="100" />
  <column number="2" width="100" startx="300" />
  <column number="3" width="100" startx="400" />
  <column number="4" width="100" startx="500" />
  <column number="5" width="100" startx="600" />
  <column number="6" width="100" startx="700" />
  <column number="7" width="100" startx="800" />
  <column number="8" width="100" startx="900" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <reportblock row="0" column="0" rowspan="4" columnspan="9" content="" css="" width="1000" height="120">
    <headblock row="0" column="0" rowspan="1" columnspan="9" content="" css="" width="1000" height="30" name="">
      <cell row="0" column="0" rowspan="1" columnspan="1" content="$隐患级别$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="1" rowspan="1" columnspan="1" content="$隐患名称$" css="tdcenter report-head head-font" width="200" height="30" />
      <cell row="0" column="2" rowspan="1" columnspan="1" content="$隐患数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="3" rowspan="1" columnspan="1" content="$用户自行处理$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="4" rowspan="1" columnspan="1" content="$安检员现场处理$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="5" rowspan="1" columnspan="1" content="$已转维修处理$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="6" rowspan="1" columnspan="1" content="$维修已处理$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="7" rowspan="1" columnspan="1" content="$未处理$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="8" rowspan="1" columnspan="1" content="$处理率$" css="tdcenter report-head head-font" width="100" height="30" />
    </headblock>
    <leftblock row="1" column="0" rowspan="3" columnspan="2" content="" css="" width="300" height="90" name="">
      <leftblock row="1" column="0" rowspan="2" columnspan="2" content="a" css="" width="300" height="60" name="left">
        <head row="1" column="0" rowspan="2" columnspan="1" content="left.yhlevel" css="tdcenter report-left head-font" width="100" height="60" />
        <leftblock row="1" column="1" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel)" css="" width="200" height="30" name="left1">
          <head row="1" column="1" rowspan="1" columnspan="1" content="left1.f_item_name" css="tdcenter report-left head-font" width="200" height="30" />
        </leftblock>
        <cell row="2" column="1" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-left head-font" width="200" height="30" />
      </leftblock>
      <cell row="3" column="0" rowspan="1" columnspan="2" content="$合计(不计算异常差值)$" css="tdcenter report-left head-font" width="300" height="30" />
    </leftblock>
    <bodyblock row="1" column="2" rowspan="3" columnspan="7" content="" css="" width="700" height="90" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="1" column="2" rowspan="2" columnspan="7" content="" css="" width="700" height="60" headexpression="" leftexpression="a" headname="" leftname="left">
        <bodyblock row="1" column="2" rowspan="1" columnspan="7" content="" css="" width="700" height="30" headexpression="" leftexpression="b.where(row.yhlevel==left.yhlevel)" headname="" leftname="left1">
          <cell row="1" column="2" rowspan="1" columnspan="1" content="left1.count" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="3" rowspan="1" columnspan="1" content="left1.zxcl" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="4" rowspan="1" columnspan="1" content="left1.xccl" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="5" rowspan="1" columnspan="1" content="left1.yzwxcl" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="6" rowspan="1" columnspan="1" content="left1.wxycl" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="7" rowspan="1" columnspan="1" content="left1.wcl" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="8" rowspan="1" columnspan="1" content="${left1.cll}%$" css="tdcenter report-main main-font" width="100" height="30" />
        </bodyblock>
        <cell row="2" column="2" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel).sum(row.count)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="3" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel).sum(row.zxcl)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="4" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel).sum(row.xccl)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="5" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel).sum(row.yzwxcl)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="6" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel).sum(row.wxycl)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="7" rowspan="1" columnspan="1" content="b.where(row.yhlevel==left.yhlevel).sum(row.wcl)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="8" rowspan="1" columnspan="1" content="${left.uu}%$" css="tdcenter report-main main-font" width="100" height="30" />
      </bodyblock>
      <cell row="3" column="2" rowspan="1" columnspan="1" content="b.sum(row.count)-b.where(row.yhlevel == $异常差值$).sum(row.count)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="3" rowspan="1" columnspan="1" content="b.sum(row.zxcl)-b.where(row.yhlevel == $异常差值$).sum(row.zxcl)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="4" rowspan="1" columnspan="1" content="b.sum(row.xccl)-b.where(row.yhlevel == $异常差值$).sum(row.xccl)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="5" rowspan="1" columnspan="1" content="b.sum(row.yzwxcl)-b.where(row.yhlevel == $异常差值$).sum(row.yzwxcl)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="6" rowspan="1" columnspan="1" content="b.sum(row.wxycl)-b.where(row.yhlevel == $异常差值$).sum(row.wxycl)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="7" rowspan="1" columnspan="1" content="b.sum(row.wcl)-b.where(row.yhlevel == $异常差值$).sum(row.wcl)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="8" rowspan="1" columnspan="1" content="${c[0].allcount}%$" css="tdcenter report-main main-font" width="100" height="30" />
    </bodyblock>
  </reportblock>
</report>