<cfg>
    <!--计划调整 -->
    <logic alias='CheckerPlanAdjustTS' path='TS/CheckerPlanAdjustTS.logic'/>
    <logic alias='deleteCheckPlanItemTS' path='TS/deleteCheckPlanItemTS.logic'/>
    <!--修改安检计划项-->
    <logic alias='UpCheckPlanItemTS' path='TS/UpCheckPlanItemTS.logic'/>
    <logic alias='getUserInfoTS' path='TS/getUserInfoTS.logic' mobile='true'/>
    <!--非民用生成安检计划项-->
    <logic alias='AddCheckPlanItemCivilTS' path='TS/AddCheckPlanItemCivilTS.logic'/>
    <logic alias='AddCheckPlanItemTS' path='TS/AddCheckPlanItemTS.logic'/>
    <logic alias='deleteCheckPlanTS' path='TS/deleteCheckPlanTS.logic'/>
    <logic alias='UpCheckPlanItemStateTS' path='TS/UpCheckPlanItemStateTS.logic'/>
    <!-- 更新安检信息 -->
    <logic alias='UpdateCheckPaperTS' path='TS/UpdateCheckPaperTS.logic' mobile='true'/>
    <!-- 上传安检信息 -->
    <logic alias='上传安检信息TS' path='TS/UpCheckPaperTS.logic' mobile='true'/>
    <!-- 批量上传安检单 -->
    <logic alias='UploadAllTS' path='TS/UploadAllTS.logic' mobile='true'/>
    <logic alias='changeGasCountTS' path='TS/changeGasCountTS.logic'/>
    <logic alias='changeGasCountwTS' path='TS/changeGasCountwTS.logic'/>


    <logic alias='createCheckPlanTS' path='TS/createCheckPlanTS.logic'/>
    <logic alias='NewBespeakCheckPlanTS' path='TS/NewBespeakCheckPlanTS.logic'/>
    <logic alias='UpdateCheckPlanTS' path='TS/UpdateCheckPlanTS.logic'/>
    <logic alias='UploadRectificationTS' path='TS/UploadRectificationTS.logic'/>
    <logic alias="SuppCheckPaperTS" path="TS/SuppCheckPaperTS.logic"/>
    <logic alias='getShareCheckPlanTS' path='TS/getShareCheckPlanTS.logic'/>
    <logic alias='BookingCheckTS' path='TS/BookingCheckTS.logic' mobile='true'/>
    <logic alias='PCbookingCheckTS' path='TS/PCbookingCheckTS.logic' mobile='true'/>
    <logic alias='SafeSaveNoPaperTS' path='TS/SaveNoPaperTS.logic' mobile='true'/>
    <logic alias='保存安检单信息TS' path='TS/SavePaperTS.logic' mobile='true'/>
    <logic alias='getPaperInfoTS' path='TS/getPaperInfoTS.logic' mobile='true'/>
    <logic alias='saveDeviceTS' path='TS/saveDeviceTS.logic' mobile='true'/>
    <logic alias='getLiveDisposeTS' path='TS/getLiveDisposeTS.logic' mobile='true'/>
    <logic alias='SafeCheckServiceTimeOutTS' path='TS/SafeCheckServiceTimeOutTS.logic' mobile='true'/>
    <logic alias='GetCheckPlanIntoToServerTS' path='TS/GetCheckPlanIntoToServerTS.logic' mobile='true'/>
    <logic alias='FetchCheckPaperTS' path='TS/FetchCheckPaperTS.logic' mobile='true'/>
    <logic alias='getCheckPlanChangedTS' path='TS/getCheckPlanChangedTS.logic' mobile='true'/>
    <logic alias='getPcCheckPaperTS' path='TS/getPcCheckPaperTS.logic' mobile='true'/>
    <logic alias='DownCheckPlanInfoTS' path='TS/DownCheckPlanInfoTS.logic' mobile='true'/>
    <!--查询本地共享计划-->
    <logic alias='getLocalShareCheckPlanTS' path='TS/getLocalShareCheckPlanTS.logic' mobile='true'/>

    <!-- ************************************path 转logic*********************************************** -->
    <!--获取安检单内容-->
    <logic alias='path_getCheckPaperTS' path='TS/pathConvertor/getCheckPaperTS.logic'/>
    <!--兼容手机端获取安检单内容(其他业务勿用)-->
    <logic alias='path_getPhoneCheckPaperTS' path='TS/pathConvertor/getPhoneCheckPaperTS.logic' mobile="true"/>
    <!--获取手机端安检待上传列表-->
    <logic alias='path_getUploadPaperListTS' path='TS/pathConvertor/getUploadPaperListTS.logic' mobile="true"/>

    <!--*********************************************以上是外部安检使用的logic********************************************************************-->


    <logic alias='下班签到' path='KnockOff.logic' mobile='true'/>

    <!--计划调整 -->
    <logic alias='CheckerPlanAdjust' path='CheckerPlanAdjust.logic'/>
    <logic alias='年计划生成' path='MakeAnnualPlan.logic'/>
    <logic alias='ApprovePaper' path='ApprovePaper.logic'/>
    <!-- 获取安检计划改变 -->
    <logic alias='getCheckPlanChanged' path='getCheckPlanChanged.logic' mobile='true'/>
    <!-- 提取安检 -->
    <logic alias='SafeCheckServiceTimeOut' path='SafeCheckServiceTimeOut.logic' mobile='true'/>

    <!-- 安检预约 -->
    <logic alias='BookingCheck' path='BookingCheck.logic' mobile='true'/>
    <!-- 信息修改，保存变更记录 -->
    <logic alias='Changelog' path='Changelog.logic'/>

    <!-- 更新安检信息 -->
    <logic alias='UpdateCheckPaper' path='UpdateCheckPaper.logic' mobile='true'/>

    <!-- PC端预约安检同步 -->
    <logic alias='PCbookingCheck' path='PCbookingCheck.logic' mobile='true'/>

    <!-- 获取服务器端共享计划 -->
    <logic alias='GetCheckPlanIntoToServer' path='GetCheckPlanIntoToServer.logic'/>
    <!-- 上传成功后修改本地安检单状态 -->
    <logic alias='FinishUpload' path='FinishUpload.logic' mobile='true'/>
    <!-- 维修结果回调接口 -->
    <logic alias='RepaireResult' path='RepaireResult.logic' auth="false"/>

    <!--民用生成安检计划项-->
    <logic alias='AddCheckPlanItem' path='AddCheckPlanItem.logic'/>
    <!--非民用生成安检计划项-->
    <logic alias='AddCheckPlanItemCivil' path='AddCheckPlanItemCivil.logic'/>

    <logic alias='AddCheckPlanItemNoYear' path='AddCheckPlanItemNoYear.logic'/>
    <!--修改安检计划项-->
    <logic alias='UpCheckPlanItem' path='UpCheckPlanItem.logic'/>

    <!--民用自动生成计划-->
    <logic alias="AddPlanCivil" path="AddPlanCivil.logic"/>
    <logic alias="OnePlanOneCheck" path="OnePlanOneCheck.logic"/>
    <logic alias="createYearPlan" path="CreateYearPlan.logic"/>

    <!--上传安检抄表图片和签名-->
    <logic alias="uploadCheckHandplan" path="uploadCheckHandplan.logic" mobile='true'/>
    <!--上传安检抄表信息-->
    <logic alias="uploadCheckHandPlanItem" path="uploadCheckHandPlanItem.logic"/>
    <!--修改隐患项状态-->
    <logic alias="updateDefectStateZGTS" path="updateDefectStateZGTS.logic"/>

    <logic alias='changeGasCount' path='changeGasCount.logic'/>
    <logic alias='changeGasCountw' path='changeGasCountw.logic'/>
    <logic alias='safe_singleTable_GroupBy' path='safe_singleTable_GroupBy.logic'/>
    <!--保存远传抄表系统基本数据-->
    <logic alias='saveRemoteSystemBaseInfo' path='interface/saveRemoteSystemBaseInfo.logic'/>
    <!--移动抄表修改状态-->
    <logic alias='updateCheckHandPlanState' path='updateCheckHandPlanState.logic'/>
    <!--地址修改后将安检单中的地址也同步修改【本地是已办不同步，服务器已检的不同步】-->
    <logic alias='updatePlanItemAddress' path='interface/updatePlanItemAddress.logic'/>
    <logic alias='clearPlanId' path='clearPlanId.logic'/>
    <!--path改logic-->
    <!--根据安检单id查询安检单所有内容-->
    <logic alias='getCheckPaperDataById' path='getCheckPaperData.logic' mobile='true'/>
    <!-- 上传安检信息_task异步上传 -->
    <logic alias='上传安检信息' path='UpCheckPaper.logic' mobile='true'/>

    <logic alias='SubAddedServicesSync' path='telephone_timer/SubAddedServicesSync.logic'/>
    <logic alias='TelUserFilesAdd' path='telephone_timer/TelUserFilesAdd.logic'/>
    <logic alias='ZhiHuanServiceWorkLogic' path='telephone_timer/ZhiHuanServiceWorkLogic.logic'/>
    <logic alias="saveSpotCheck" path="SaveSpotCheck.logic" />

</cfg>
