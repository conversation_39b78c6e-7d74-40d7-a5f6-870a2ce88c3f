select
ui.f_userinfo_id,
ui.f_userinfo_code,
tua.f_address,
ui.f_user_name,
ui.f_user_phone,
ui.f_idnumber,
ui.f_createfile_date,
ui.f_orgname,
n
from t_userinfo ui WITH(nolock)
left join t_user_address tua WITH(nolock) on ui.f_userinfo_id=tua.f_userinfo_id
left join(
select count(f_userinfoid) n,f_userinfoid from t_check_plan_item tcpi WITH(nolock) group by tcpi.f_userinfoid
)c on ui.f_userinfo_id=c.f_userinfoid
where {condition}
{
	f_filialeid != $$ :
	$ and ui.f_filialeid  in {f_filialeid} $,
	$ $
}
order by ui.f_userinfo_id
