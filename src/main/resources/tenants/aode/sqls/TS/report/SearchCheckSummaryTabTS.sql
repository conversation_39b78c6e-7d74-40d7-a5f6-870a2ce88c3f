--- 按月统计每月安检数和截至到去年年底总用户数所计算的入户率，和统计的全年安检数

SELECT t1.Mon,
       ISNULL(t5.sumUserinfo, 0)   sumUserinfo,        --截止到去年正常用户数
       ISNULL(t2.checkCount, 0)    checkCount,         -- 每月安检数
       CONCAT(
               ( CASE WHEN (CAST(ISNULL(t5.sumUserinfo, 0) AS int) * 0.1)=0 THEN 0
                   ELSE
                   CAST(((CAST(ISNULL(t2.checkCount, 0) AS int) * 0.1) /
                         (CAST(ISNULL(t5.sumUserinfo, 0) AS int) * 0.1)) * 100 AS numeric(18, 2))
                  END  ), '%')         checkCompletion,    -- 每月的入户率
       ISNULL(t3.sumCheckCount, 0) sumCheckCount,      -- 本年度安检入户总和
       CONCAT(
               (  CASE WHEN (CAST(ISNULL(t5.sumUserinfo, 0) AS int) * 0.1)=0 THEN 0
                   ELSE
                   CAST(((CAST(ISNULL(t3.sumCheckCount, 0) AS int) * 0.1) /
                         (CAST(ISNULL(t5.sumUserinfo, 0) AS int) * 0.1)) * 100 AS numeric(18, 2))
                 END  ), '%')         sumCheckCompletion, -- 本年度案件入户率
       ISNULL(t3.noVisit, 0)       noVisit,            --本年度到访不遇总和
       CONCAT(
               ( CASE  WHEN (CAST(ISNULL(t5.sumUserinfo, 0) AS int) * 0.1)=0 THEN 0
                   ELSE
                   CAST(((CAST(ISNULL(t3.noVisit, 0) AS int) * 0.1) / (CAST(ISNULL(t5.sumUserinfo, 0) AS int) * 0.1)) *
                        100 AS numeric(18, 2))
                 END  ), '%')         noVisitCompletion   --年度到访不遇率
FROM (SELECT 1 as Mon
      UNION ALL
      SELECT 2 as Mon
      UNION ALL
      SELECT 3 as Mon
      UNION ALL
      SELECT 4 as Mon
      UNION ALL
      SELECT 5 as Mon
      UNION ALL
      SELECT 6 as Mon
      UNION ALL
      SELECT 7 as Mon
      UNION ALL
      SELECT 8 as Mon
      UNION ALL
      SELECT 9 as Mon
      UNION ALL
      SELECT 10 as Mon
      UNION ALL
      SELECT 11 as Mon
      UNION ALL
      SELECT 12 as Mon) t1
         FULL JOIN
     (SELECT MONTH (f_upload_date) Mon, COUNT (1) checkCount
      FROM t_check_paper_ts
      WITH (nolock)
          --查询本年记录【时间需要更改】 已入户 并且类型是年度普检的记录
      WHERE f_upload_date >= '{yearDate}-01-01 00:00:00'
        and f_upload_date <= '{yearDate}-12-31 23:59:59'--DATEDIFF(YEAR,f_upload_date,GETDATE())=0
        AND f_entry_status = '入户'
        AND f_safecheck_type = '{f_safecheck_type}'
        AND f_filialeid in {f_filialeid_id} { f_check_type!= $$: $AND f_check_type = '{f_check_type}' $
          , $ $ }
      GROUP BY
          MONTH (f_upload_date) -- 按月分组
     ) t2 ON t1.Mon = t2.Mon
         FULL JOIN (SELECT sum(a) sumCheckCount,
                           sum(c) noVisit
                    from (SELECT MONTH (f_upload_date) Mon, case when f_entry_status = '入户' then 1 else 0 end a, case when f_entry_status = '到访不遇' then 1 else 0 end c
                          FROM (
                              SELECT * FROM (
                              -- 根据结果表中的f_check_item_id分组并根据上传时间排序，取最后一次上传时间的状态，进行按月统计,因到访不遇计划，重新派发三次后本次才算结束
                              SELECT *, row_number() over(partition by MONTH (f_upload_date), f_userinfoid order by f_upload_date desc) rn FROM t_check_paper_ts WITH (nolock)
                              WHERE f_upload_date >= '{yearDate}-01-01 00:00:00' and f_upload_date <= '{yearDate}-12-31 23:59:59' and f_check_item_id is not null
                              AND f_safecheck_type = '{f_safecheck_type}' AND f_filialeid in {f_filialeid_id}
                              { f_check_type != $$: $AND f_check_type = '{f_check_type}' $, $ $ }
                              ) tt2 where tt2.rn = 1
                              ) t1
                             --查询本年记录【时间需要更改】 已入户 并且类型是年度普检的记录
                         ) temp) t3 ON 1 = 1
         FULL JOIN
     (
         -- 截止到去年年底的所有用户数
         SELECT COUNT(1) sumUserinfo
         FROM t_userinfo_ts ui WITH (nolock) LEFT JOIN t_userfiles_ts uf
         WITH (nolock)
         ON ui.f_userinfo_id = uf.f_userinfo_id
         WHERE ui.f_filialeid in {f_filialeid_id}
           AND ui.f_user_state = '正常' { f_check_type!= $$: $
           AND uf.f_user_type = '{f_check_type}' $
             , $ $ }
           AND
             uf.f_table_state = '正常'
           AND uf.f_open_date <= (SELECT dateadd(ms
             , -3
             , DATEADD(yy
             , DATEDIFF(yy
             , 0
             , '{yearDate}-01-01 00:00:00')
             , 0)))) t5 ON 1 = 1
ORDER BY Mon

