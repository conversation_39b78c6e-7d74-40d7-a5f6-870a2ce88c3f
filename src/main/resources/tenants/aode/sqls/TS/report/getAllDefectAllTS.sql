SELECT CONVERT(NUMERIC (5, 2), (convert(float, sum(zxzgcl)) + convert(float, sum(yzwxcl)) + convert(float, sum(wxycl)) +
                                convert(float, sum(zxccl))) * (convert(float, 100)) / (case
                                                                                           when convert(float,
                                                                                                        count(0) -
                                                                                                        (SUM(CASE WHEN yhlevel = '异常差值' THEN 1 ELSE 0 END))) =
                                                                                                0 then 1
                                                                                           else convert(float,
                                                                                                        count(0) -
                                                                                                        (SUM(CASE WHEN yhlevel = '异常差值' THEN 1 ELSE 0 END))) END)) AS allcount
FROM (SELECT case
                 when f_defect_level like '一级%' then '一级隐患'
                 when f_defect_level like '二级%' then '二级隐患'
                 when f_defect_level like '三级%' then '三级隐患'
                 when f_item_name like '%异常差值' then '异常差值'
                 else '未定级隐患' end as yhlevel,
             tdi.f_item_name,
             CASE
                 WHEN f_user_changes = '是' and f_item_name not LIKE '%异常差值' THEN
                     1
                 ELSE 0
                 END                   AS zxzgcl,
          CASE
          WHEN f_is_repaired = '已转维修' and f_item_name not LIKE '%异常差值' THEN
          1 ELSE 0
          END AS yzwxcl,
          CASE
          WHEN f_is_repaired = '维修已处理' and f_item_name not LIKE '%异常差值' THEN
          1 ELSE 0
          END AS wxycl,
          CASE
          WHEN f_is_repaired = '需转维修' and f_item_name not LIKE '%异常差值' THEN
          1 ELSE 0
          END AS xzwxcl,
        CASE
            WHEN f_live_dispose = '是' and f_item_name not LIKE '%异常差值' THEN
            1 ELSE 0
            END AS zxccl
      FROM
          t_check_paper_ts tcp
      WITH (nolock)
          LEFT JOIN t_paper_devices_ts tpd
      WITH (nolock)
      ON tcp.id= tpd.f_paper_id
          LEFT JOIN t_devices_items_ts tdi
      WITH (nolock)
      ON tpd.id= tdi.f_device_id
      WHERE
          tdi.f_is_defect = 'true'
        and tcp.f_offsite_time>='{startDate} 00:00:00'
        and tcp.f_offsite_time<='{endDate} 23:59:59'
        and tcp.f_filialeid in {f_filialeid}
        and {condition}) tt
