--预约计划查用户信息

select
    id,
    f_plan_name,
    case f_issued
        when '是' then '已下发'
        when '否' then '未下发'
    end
    as f_issued,
    f_plan_year,
    f_plan_month,
    f_checker,
    f_checker_id,
    (SELECT count(id) FROM t_check_plan_item_ts WHERE f_plan_id = cp.id and f_state !='作废') plan_count,
    (SELECT count(id) FROM t_check_plan_item_ts WHERE f_plan_id = cp.id and f_state ='已检') checked,
    (SELECT count(id) FROM t_check_plan_item_ts WHERE f_plan_id = cp.id and f_state ='未检') unchecked,
    (SELECT count(id) FROM t_check_plan_item_ts WHERE f_plan_id = cp.id ) plan_allcount,
    cp.f_safecheck_type
from t_check_plan_ts cp
where {condition}
and f_plan_type = '预约计划'
and f_filialeid in {f_filialeids}
{
groupitem !=$$:
$,
group by {groupitem}
$,
groupitem ==$$:
$$,
$$
}
order by {orderitem}
