SELECT
    count(0) total,
    isnull(sum(case when uf.f_user_type='民用' then 1 else 0 end),0) civilCount,
    isnull(sum(case when uf.f_user_type='非民用' then 1 else 0 end),0) noCivilCount,
    isnull(sum(case when uf.f_user_type!='民用' and uf.f_user_type!='非民用' then 1 else 0 end),0) elseCount
FROM
    t_userinfo_ts ui WITH ( nolock )
	left join t_userfiles_ts uf WITH ( nolock ) on ui.f_userinfo_id=uf.f_userinfo_id and uf.f_table_state='正常'
    left join t_user_address_ts ua WITH ( nolock ) on ui.f_userinfo_id=ua.f_userinfo_id
    LEFT JOIN (
    SELECT
    *
    FROM
    (
    SELECT
    id,
    f_check_plan_id,
    f_check_version,
    f_checker_name,
    f_defect_content,
    f_checker_id,
    f_userinfoid,
    f_offsite_time,
    f_check_item_id,
    f_onsite_time,
    f_entry_status,
    f_safecheck_type,
    ROW_NUMBER ( ) OVER ( partition BY f_userinfoid ORDER BY f_offsite_time DESC ) AS rn
    FROM
    t_check_paper_ts
    where {papercondition}
    ) AS u WHERE u.rn=1
    ) cp ON ui.f_userinfo_id= cp.f_userinfoid
    LEFT JOIN t_check_plan_ts tcp WITH ( nolock ) ON cp.f_check_plan_id= tcp.id
    LEFT JOIN (
    SELECT
    f_paper_id,
    MAX ( a.[是否为热水器用户] ) AS reshuiqi,
    MAX ( a.[是否为壁挂炉用户] ) AS bigualu
    FROM
    (
    SELECT
    f_paper_id,
    tdi.f_item_name,
    tdil.f_item_value
    FROM
    t_paper_devices_ts tpd WITH ( nolock )
    LEFT JOIN t_devices_items_ts tdi WITH ( nolock ) ON tpd.id= tdi.f_device_id
    LEFT JOIN t_devices_items_lists_ts tdil WITH ( nolock ) ON tdi.id= tdil.f_item_id
    WHERE
    f_device_type = '安检详情'
    ) tt PIVOT ( MAX ( tt.f_item_value ) FOR tt.f_item_name IN ( [是否为热水器用户], [是否为壁挂炉用户] ) ) a
    GROUP BY
    a.f_paper_id
    ) xiangqing ON cp.id= xiangqing.f_paper_id
    LEFT JOIN ( SELECT case when COUNT ( * ) is null then 0 else  COUNT ( * ) end send_num, f_userinfoid FROM t_check_plan_item_ts WITH ( nolock ) WHERE { itemcondition } GROUP BY f_userinfoid ) send ON ui.f_userinfo_id= send.f_userinfoid
    LEFT JOIN ( SELECT case when COUNT ( * ) is null then 0 else  COUNT ( * ) end count_item, f_userinfoid FROM t_check_paper_ts WITH ( nolock ) WHERE { papercondition } GROUP BY f_userinfoid ) cou ON ui.f_userinfo_id= cou.f_userinfoid
WHERE
    { condition}
