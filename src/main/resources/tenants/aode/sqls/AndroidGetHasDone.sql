SELECT  m.* FROM ( SELECT tm.f_meter_no,ti.*,cp.f_plan_name,cp.f_plan_year,cp.f_plan_month,tcp.f_upload_date FROM t_check_plan_item ti LEFT JOIN t_plan_item_meter tm ON tm.f_plan_item_id = ti.id left join t_check_plan cp on cp.id = ti.f_plan_id left join t_check_paper tcp on tcp.f_check_item_id = ti.id WHERE {condition} and ti.f_state = '已检' and  ti.f_complete = '已完成' ) m
  ORDER BY m.f_residential_area,m.f_building+0,m.f_unit+0,m.f_floor+0,m.f_room+0
