select
    cbs, --抄表数
    khs, --开户总数
    khs - cbs AS wcbs, --未抄表数
    case when khs= 0 then '0%' else cast(cast(cast(cbs as float)/khs*100 as NUMERIC(6,2)) as varchar) + '%' end  cbsrate    --接线率
from (
         SELECT
             MAX(CASE WHEN rn = 1 THEN cbs ELSE NULL END) AS cbs, --抄表数
             MAX(CASE WHEN rn = 2 THEN khs ELSE NULL END) AS khs  --开户总数
         FROM (
                  select
                      count(*) AS cbs, --抄表数
                      NULL AS khs,
                      1 AS rn -- 为第一个子查询添加行号
                  from  (
                            SELECT
                                tc.f_userinfo_id
                            FROM t_check_handplan tc
                            WHERE tc.f_userinfo_id IN (
                                SELECT tu.f_userinfo_id
                                FROM t_userinfo tu
                                         LEFT JOIN t_userfiles tf ON tu.f_userinfo_id = tf.f_userinfo_id
                                WHERE tu.f_user_state = '正常'
                                  AND tf.f_user_type = '非民用'
                                  AND tu.f_createfile_date < CAST(DATEADD(DAY, 24 - DAY(GETDATE()), CAST(GETDATE() AS DATE)) AS DATETIME) + '23:59:59'
                                  and tu.f_filialeid IN { f_filialeid }
                            )
                              AND f_upload_date < CAST(EOMONTH(GETDATE()) AS DATETIME) + '23:59:59'
                            GROUP BY f_userinfo_id
                        )  yy
                  UNION ALL
                  SELECT
                      NULL AS cbs,
                      count(*) AS khs, --开户总数（截止到当月24日23点59分）（户）
                      2 AS rn -- 为第二个子查询添加行号
                  FROM t_userinfo tu
                           LEFT JOIN t_userfiles tf ON tu.f_userinfo_id = tf.f_userinfo_id
                  WHERE tu.f_user_state = '正常'
                    AND tf.f_user_type = '非民用'
                    AND tu.f_createfile_date < CAST(DATEADD(DAY, 24 - DAY(GETDATE()), CAST(GETDATE() AS DATE)) AS DATETIME) + '23:59:59'
                    and tu.f_filialeid IN { f_filialeid }
              ) tt
     )cc
