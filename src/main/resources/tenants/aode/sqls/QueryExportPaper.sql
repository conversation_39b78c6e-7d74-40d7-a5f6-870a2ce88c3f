select f_entry_status,f_userinfo_code,f_user_name,f_onsite_time,f_offsite_time,f_upload_date,f_checker_name,f_check_type,f_address from t_check_paper WITH(nolock)
where {condition}
{
	f_plan_id == $$ :
	$$,
	$
	 and f_check_plan_id = '{f_plan_id}'
	$
}
{
	f_approved == $$ :
	$$,
	$
	 and f_approved = '{f_approved}'
	$
}
{
	f_entry_status == $$ :
	$$,
	$
	 and f_entry_status = '{f_entry_status}'
	$
}
{
	f_no_checkplan == $$ :
	$$,
	$
	 and f_no_checkplan = '{f_no_checkplan}'
	$
}
{
	f_filialeid == $$ :
	$$,
	$
	 and f_filialeid like '{f_filialeid}%'
	$
}
order by f_offsite_time desc
