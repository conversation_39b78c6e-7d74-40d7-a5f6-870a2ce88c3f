--安检情况汇总
--分组项：f_plan_month计划月份，f_no_checkplan计划类型，f_residential_area小区，f_plan_name计划名称
--        f_checker_name安检员
--求和项：f_user_number 档案数，f_plan_total计划总数，f_weijian未检计划数，f_yijian已检计划数
--        f_ruhu入户数，f_buyu到访不遇数，f_jujian拒检数，安检率
select
    count(*) f_plan_total,
    sum(f_weijian) f_weijian,
    sum(f_yijian) f_yijian,
    max(uu.f_year_number) as y<PERSON><PERSON><PERSON><PERSON><PERSON>,--应检户数：截止到去年12月31日已开通建档户数
    max(uuu.f_year_yj_number) as yiji<PERSON><PERSON><PERSON>,--已检户数：当年已入户安检的用户数
    max(uuuu.f_year_wxf_number) as weix<PERSON><PERSON><PERSON><PERSON>,--未下发户数：应捡数中一次计划也没下发的用户
    sum(f_ruhu) f_ruhu,
    sum(f_buyu) f_buyu,
    sum(f_jujian) f_jujian,
    cast(Convert(decimal(18,2),case when MAX(f_year_number) is null then 0.00 else sum(f_ruhu)/(MAX(f_year_number)+0.00)*100.00 end)  as varchar)+'%'   f_check_bv,
    {groupName}
from(
    select
    cpi.f_no_checkplan,
    cpi.f_residential_area,
    cpl.f_plan_name,
    cpi.f_filialeid,
    cast(cpl.f_plan_year as varchar)+'年'+cast(cpl.f_plan_month as varchar)+'月' f_plan_month,
    cpall.f_checker_name,
    cpl.f_checker,
    case when (cpi.f_state='未检' or (cpall.f_entry_status='到访不遇' and cpall.f_check_version < 2)) then 1 else 0 end f_weijian,
    case when cpi.f_state='已检' then 1 else 0 end f_yijian,
    case when f_entry_status='入户' then 1 else 0 end f_ruhu,
    case when cpall.f_entry_status='到访不遇' and cpall.f_check_version > 1 then 1 else 0 end f_buyu,
    case when f_entry_status='拒检' then 1 else 0 end f_jujian
    from
    t_check_plan_item cpi WITH(nolock)
    left join t_check_plan cpl WITH(nolock) on cpi.f_plan_id = cpl.id
    left join (select f_check_item_id,max(f_offsite_time) f_offsite_time from t_check_paper WITH(nolock) group by f_check_item_id) cp on cpi.id=cp.f_check_item_id
    left join t_check_paper cpall WITH(nolock) on cp.f_check_item_id = cpall.f_check_item_id and cp.f_offsite_time = cpall.f_offsite_time
    where
    cpi.f_state!='作废'
    { f_plan_year != null && f_plan_year != 0:$ and cpl.f_plan_year = '{f_plan_year}'$,$$}
    ) ch left join (
    select
    uf.f_gasproperties,
    ua.f_residential_area area,
    count(0) f_user_number
    from t_user_address ua WITH(nolock)
    left join t_userinfo u WITH(nolock) on ua.f_userinfo_id=u.f_userinfo_id
    LEFT JOIN t_userfiles uf WITH(nolock) on u.f_userinfo_id=uf.f_userinfo_id
    -- 只统计开户了，并且开户日期小于当前日期的 （排除通过置换单开户的预备户）
    where f_user_state='正常' and f_table_state='正常' and u.f_open_date <= GETDATE()
    group by ua.f_residential_area,uf.f_gasproperties
    ) u on ch.f_residential_area = u.area
    left join (
    select
    uf.f_gasproperties as uu_f_gasproperties,
    ua.f_residential_area area,
    count(1) f_year_number
    from t_user_address ua WITH(nolock)
    left join t_userinfo u WITH(nolock) on ua.f_userinfo_id=u.f_userinfo_id
    LEFT JOIN t_userfiles uf WITH(nolock) on u.f_userinfo_id=uf.f_userinfo_id
    -- 只统计开户了，并且开户日期在今年之前的
    where f_user_state='正常' and f_table_state='正常'
    { f_plan_year != null && f_plan_year != 0:$ and year(u.f_open_date) < {f_plan_year}$,$$}
    group by ua.f_residential_area,uf.f_gasproperties
    ) uu on ch.f_residential_area = uu.area

    left join (
    --当年已入户安检的用户数，无论哪种计划入户的都算，且数量不能重复; (目的是想排查一次没安检的，需要排除重复安检户)
    select
    uf.f_gasproperties as uuu_f_gasproperties,
    ua.f_residential_area area,
    count(1) f_year_yj_number--年度已检
    from t_user_address ua WITH(nolock)
    left join t_userinfo u WITH(nolock) on ua.f_userinfo_id=u.f_userinfo_id
    LEFT JOIN t_userfiles uf WITH(nolock) on u.f_userinfo_id=uf.f_userinfo_id
    left join (select count(0) check_count,f_userinfoid from t_check_paper where f_entry_status = '入户' { f_plan_year != null && f_plan_year != 0:$ and year(f_offsite_time) = {f_plan_year}$,$$}
    group by f_userinfoid) tcp on tcp.f_userinfoid = u.f_userinfo_id and check_count > 0
    where f_user_state='正常' and f_table_state='正常'
    group by ua.f_residential_area,uf.f_gasproperties
    ) uuu on ch.f_residential_area = uuu.area

    left join (
    select
    uf.f_gasproperties as uuuu_f_gasproperties,
    ua.f_residential_area area,
    count(1) f_year_wxf_number--未下发过计划,并且开户时间是今年之前
    from t_user_address ua WITH(nolock)
    left join t_userinfo u WITH(nolock) on ua.f_userinfo_id=u.f_userinfo_id
    LEFT JOIN t_userfiles uf WITH(nolock) on u.f_userinfo_id=uf.f_userinfo_id
    left join t_check_plan_item tcpl on u.f_userinfo_code = tcpl.f_userinfo_code
    -- 只统计开户了，并且开户日期在今年之前的
    where f_user_state='正常' and f_table_state='正常' and tcpl.id is null
    { f_plan_year != null && f_plan_year != 0:$ and year(u.f_open_date) < {f_plan_year}$,$$}
    group by ua.f_residential_area,uf.f_gasproperties
    ) uuuu on ch.f_residential_area = uuuu.area

where {condition}
group by {groupName}
order by {groupName}
