select t.f_subcompany f_subcompany, t.f_plan_year f_plan_year,
sum(cplan) cplan, sum(call) call, sum(centry) centry, sum(cdeny) cdeny, sum(cabsent) cabsent, sum(ctoberepaired) ctoberepaired, sum(crepaired),
sum(clevel1) clevel1, sum(clevel2) clevel2, sum(clevel3) clevel3 FROM
(
select p.f_subcompany f_subcompany, f_plan_year, count(*) cplan
from t_check_plan p left join t_check_plan_item pi on p.id = pi.f_plan_id where
{
    f_subcompany !=$$:
    $
		p.f_subcompany='{f_subcompany}' and
    $,
    $$
}
{
    f_plan_year !=$$:
    $
		f_plan_year={f_plan_year}
    $,
    $$
}
group by p.f_subcompany, f_plan_year
) t LEFT JOIN
(
select f_subcompany, CAST(SUBSTRING(f_offsite_time, 1, 4) as int) f_plan_year,
count(*) call,
sum(case when f_entry_status = '入户' then 1 else 0 end) centry,
sum(case when f_entry_status = '拒检' then 1 else 0 end) cdeny,
sum(case when f_entry_status = '到访不遇' then 1 else 0 end) cabsent,
sum(case when f_repairman is not null then 1 else 0 end) ctoberepaired,
sum(case when f_repairman is not null and f_repaired='已修' then 1 else 0 end) crepaired,
sum(case when f_defect_urgency='A' then 1 else 0 end) clevel1,
sum(case when f_defect_urgency='B' then 1 else 0 end) clevel2,
sum(case when f_defect_urgency='C' then 1 else 0 end) clevel3
from t_check_paper
where
{
    f_subcompany !=$$:
    $
		 f_subcompany='{f_subcompany}' and
    $,
    $$
}
{
    f_plan_year !=$$:
    $
		CAST(SUBSTRING(f_offsite_time, 1, 4) as int)={f_plan_year} and
    $,
    $$
}
 f_approved='已审核'
group by f_subcompany, CAST(SUBSTRING(f_offsite_time, 1, 4) as int)
) tt on tt.f_subcompany = t.f_subcompany and tt.f_plan_year = t.f_plan_year
group by t.f_subcompany, t.f_plan_year
order by t.f_subcompany, t.f_plan_year
