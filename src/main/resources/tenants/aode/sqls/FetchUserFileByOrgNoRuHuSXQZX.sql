select
	ui.f_userinfo_id,
	f_userinfo_code,
	ua.f_area,
	ua.f_street,
	ua.f_building,
	ua.f_unit,
	ua.f_residential_area,
	ua.f_floor,
	ua.f_room,
	ua.f_address,
-- 	f_user_family_remarks,
--		de.f_devices_type,
	{
	month != $$ :
	$
	1 code,
	$,
	$$
	}
	f_user_name,
	f_user_phone,
	ua.f_last_check_state,
	ua.f_last_check_date,
	ua.f_last_check_result,
  ui.f_comments,
	ui.version,
	ua.id,
	uf.f_user_type
--	ui.f_filiale
 from
		t_userinfo ui with (nolock)
    left join t_user_address ua with (nolock) on ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles uf with (nolock) on uf.f_userinfo_id=ui.f_userinfo_id
where {condition} and (uf.f_table_state='正常' or uf.f_table_state='停用')
{
		f_usertype != $$:
		$
		And uf.f_user_type = '{f_usertype}' $,
		$$
}
{
	month != $$ :
	$
	and  datediff(month,ua.f_last_check_date, getdate())>={month}
	$,
	month == $$ :
	$
  and	1=1
	$,
	$$
}
{
	SafeCheckSX.f_check_start != $$ &&  SafeCheckSX.f_check_end != $$ && SafeCheckCQ == $$ :
  $
	and  ( isnull(ua.f_last_check_date,f_createfile_date) <= '{SafeCheckSX.f_check_start}' or isnull(f_last_check_date,f_createfile_date) >= '{SafeCheckSX.f_check_end}' or isnull(f_last_check_date,f_createfile_date) is null )
	$,
  SafeCheckSX.f_check_start != $$ &&  SafeCheckSX.f_check_end != $$ && SafeCheckCQ == $是$ :
  $
  and  ( isnull(ua.f_last_check_date,f_createfile_date) <= '{SafeCheckSX.f_check_start}' or isnull(f_last_check_date,f_createfile_date) >= '{SafeCheckSX.f_check_end}' or isnull(f_last_check_date,f_createfile_date) is null )
  and  ( DATEADD(month,{safeCycle},isnull(f_last_check_date,f_createfile_date)) <= GETDATE())
  $,
  SafeCheckSX.f_check_start != $$ &&  SafeCheckSX.f_check_end != $$ && SafeCheckCQ == $否$ :
  $
  and  ( isnull(ua.f_last_check_date,f_createfile_date) <= '{SafeCheckSX.f_check_start}' or isnull(f_last_check_date,f_createfile_date) >= '{SafeCheckSX.f_check_end}' or isnull(f_last_check_date,f_createfile_date) is null )
  and ( DATEADD(month,{safeCycle},isnull(f_last_check_date,f_createfile_date)) >= GETDATE())
  $,
	SafeCheckSX.f_check_start == $$ && SafeCheckSX.f_check_end != $$:
	$
  and	 (isnull(ua.f_last_check_date,f_createfile_date) >= '{SafeCheckSX.f_check_end}' or isnull(f_last_check_date,f_createfile_date) is null)
	$,
		SafeCheckSX.f_check_start != $$ && SafeCheckSX.f_check_end == $$:
	$
  and	 (isnull(ua.f_last_check_date,f_createfile_date) <= '{SafeCheckSX.f_check_start}' or isnull(f_last_check_date,f_createfile_date) is null)
	$,
	$$
}
order by {orderitem}
