<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <!-- 注意，该配置仅用作项目启动时的缺省配置，项目运行的实际日志通过Log4jInNacosConfigFactory配置 -->
    <appenders>
        <!-- 输出控制台配置 -->
        <Console name="Console" target="SYSTEM_OUT">
            <!-- 控制台只输出debug及以上级别的信息 -->
            <ThresholdFilter level="debug"/>
            <!-- 输出日志的格式 -->
            <PatternLayout pattern="%d [%p] - [%l] - %t - %m%n"/>
        </Console>
    </appenders>
    <loggers>
        <!--过滤掉spring的一些无用的DEBUG信息-->
        <logger name="com.af" level="info"/>
        <Logger name="org.springframework" level="error"/>
        <Logger name="com.microsoft.sqlserver" level="error"/>
        <Logger name="com.clickhouse" level="error"/>
        <Logger name="org.hibernate" level="error"/>
        <!-- 根节点 -->
        <root level="WARN">
            <!-- 定义对日志信息如何处理 -->
            <appender-ref ref="Console"/>
        </root>
    </loggers>
</configuration>
