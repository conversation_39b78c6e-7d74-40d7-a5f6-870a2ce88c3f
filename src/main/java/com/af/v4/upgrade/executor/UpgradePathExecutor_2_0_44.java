package com.af.v4.upgrade.executor;


import com.af.v4.system.runtime.dto.UpgradeField;
import com.af.v4.system.runtime.upgrade.UpgradePathExecutor;

import java.util.ArrayList;
import java.util.List;

public class UpgradePathExecutor_2_0_44 extends UpgradePathExecutor {
    @Override
    public String targetVersion() {
        return "2.0.44";
    }

    @Override
    public void upgrade() {
        // 定义字段结构
        List<UpgradeField> fields = new ArrayList<>();
        fields.add(new UpgradeField("t_order_safecheck", "f_accept_over_date", "datetime"));
        fields.add(new UpgradeField("t_order_safecheck", "f_change_order_mark", "varchar(20)"));
        fields.add(new UpgradeField("t_order_safecheck", "f_work_order_number", "varchar(50)"));
        addFields(fields);
    }
}
