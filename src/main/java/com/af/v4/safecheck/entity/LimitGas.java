package com.af.v4.safecheck.entity;

import java.util.Date;

/**
 * 限气表实体类
 * 对应数据库表: t_limit_gas
 */
public class LimitGas {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 第三方支付
     */
    private Integer fThirdPay;
    
    /**
     * 时间类型
     */
    private String fTimeType;
    
    /**
     * 时间值
     */
    private String fTimeValue;
    
    /**
     * 限制次数
     */
    private String fLimitTimes;
    
    /**
     * 限制类型
     */
    private String fLimitType;
    
    /**
     * 限制值
     */
    private String fLimitValue;
    
    /**
     * 开始日期
     */
    private Date fStartDate;
    
    /**
     * 价格ID
     */
    private Integer fPriceId;
    
    /**
     * 用户信息ID
     */
    private Integer fUserinfoId;
    
    /**
     * 限制金额
     */
    private String fLimitAmount;
    
    /**
     * 限制样式
     */
    private String fLimitStyle;
    
    /**
     * 状态
     */
    private String fState;
    
    /**
     * 操作人员
     */
    private String fOperatePeople;
    
    /**
     * 操作日期
     */
    private Date fOperateDate;
    
    /**
     * 取消人员
     */
    private String fCancelPeople;
    
    /**
     * 取消日期
     */
    private Date fCancleDate;
    
    /**
     * 操作人员ID
     */
    private String fOperatePeopleid;
    
    /**
     * 操作原因
     */
    private String fOperateReason;
    
    /**
     * 取消人员ID
     */
    private String fCancelPeopleid;
    
    /**
     * 取消原因
     */
    private String fCancelReason;
    
    /**
     * 限制来源
     */
    private String fLimitSource;
    
    /**
     * 限制渠道
     */
    private String fLimitChannel;
    
    /**
     * 限制记录ID
     */
    private String fLimitRecodId;
    
    /**
     * 取消记录ID
     */
    private String fCancleRecordId;
    
    /**
     * 业务
     */
    private String fBusiness;
    
    /**
     * 限制备注
     */
    private String fLimitComments;

    // 构造函数
    public LimitGas() {
    }

    // Getter 和 Setter 方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getfThirdPay() {
        return fThirdPay;
    }

    public void setfThirdPay(Integer fThirdPay) {
        this.fThirdPay = fThirdPay;
    }

    public String getfTimeType() {
        return fTimeType;
    }

    public void setfTimeType(String fTimeType) {
        this.fTimeType = fTimeType;
    }

    public String getfTimeValue() {
        return fTimeValue;
    }

    public void setfTimeValue(String fTimeValue) {
        this.fTimeValue = fTimeValue;
    }

    public String getfLimitTimes() {
        return fLimitTimes;
    }

    public void setfLimitTimes(String fLimitTimes) {
        this.fLimitTimes = fLimitTimes;
    }

    public String getfLimitType() {
        return fLimitType;
    }

    public void setfLimitType(String fLimitType) {
        this.fLimitType = fLimitType;
    }

    public String getfLimitValue() {
        return fLimitValue;
    }

    public void setfLimitValue(String fLimitValue) {
        this.fLimitValue = fLimitValue;
    }

    public Date getfStartDate() {
        return fStartDate;
    }

    public void setfStartDate(Date fStartDate) {
        this.fStartDate = fStartDate;
    }

    public Integer getfPriceId() {
        return fPriceId;
    }

    public void setfPriceId(Integer fPriceId) {
        this.fPriceId = fPriceId;
    }

    public Integer getfUserinfoId() {
        return fUserinfoId;
    }

    public void setfUserinfoId(Integer fUserinfoId) {
        this.fUserinfoId = fUserinfoId;
    }

    public String getfLimitAmount() {
        return fLimitAmount;
    }

    public void setfLimitAmount(String fLimitAmount) {
        this.fLimitAmount = fLimitAmount;
    }

    public String getfLimitStyle() {
        return fLimitStyle;
    }

    public void setfLimitStyle(String fLimitStyle) {
        this.fLimitStyle = fLimitStyle;
    }

    public String getfState() {
        return fState;
    }

    public void setfState(String fState) {
        this.fState = fState;
    }

    public String getfOperatePeople() {
        return fOperatePeople;
    }

    public void setfOperatePeople(String fOperatePeople) {
        this.fOperatePeople = fOperatePeople;
    }

    public Date getfOperateDate() {
        return fOperateDate;
    }

    public void setfOperateDate(Date fOperateDate) {
        this.fOperateDate = fOperateDate;
    }

    public String getfCancelPeople() {
        return fCancelPeople;
    }

    public void setfCancelPeople(String fCancelPeople) {
        this.fCancelPeople = fCancelPeople;
    }

    public Date getfCancleDate() {
        return fCancleDate;
    }

    public void setfCancleDate(Date fCancleDate) {
        this.fCancleDate = fCancleDate;
    }

    public String getfOperatePeopleid() {
        return fOperatePeopleid;
    }

    public void setfOperatePeopleid(String fOperatePeopleid) {
        this.fOperatePeopleid = fOperatePeopleid;
    }

    public String getfOperateReason() {
        return fOperateReason;
    }

    public void setfOperateReason(String fOperateReason) {
        this.fOperateReason = fOperateReason;
    }

    public String getfCancelPeopleid() {
        return fCancelPeopleid;
    }

    public void setfCancelPeopleid(String fCancelPeopleid) {
        this.fCancelPeopleid = fCancelPeopleid;
    }

    public String getfCancelReason() {
        return fCancelReason;
    }

    public void setfCancelReason(String fCancelReason) {
        this.fCancelReason = fCancelReason;
    }

    public String getfLimitSource() {
        return fLimitSource;
    }

    public void setfLimitSource(String fLimitSource) {
        this.fLimitSource = fLimitSource;
    }

    public String getfLimitChannel() {
        return fLimitChannel;
    }

    public void setfLimitChannel(String fLimitChannel) {
        this.fLimitChannel = fLimitChannel;
    }

    public String getfLimitRecodId() {
        return fLimitRecodId;
    }

    public void setfLimitRecodId(String fLimitRecodId) {
        this.fLimitRecodId = fLimitRecodId;
    }

    public String getfCancleRecordId() {
        return fCancleRecordId;
    }

    public void setfCancleRecordId(String fCancleRecordId) {
        this.fCancleRecordId = fCancleRecordId;
    }

    public String getfBusiness() {
        return fBusiness;
    }

    public void setfBusiness(String fBusiness) {
        this.fBusiness = fBusiness;
    }

    public String getfLimitComments() {
        return fLimitComments;
    }

    public void setfLimitComments(String fLimitComments) {
        this.fLimitComments = fLimitComments;
    }
}
