package com.af.v4.safecheck.controller;

import com.af.v4.safecheck.entity.LimitGas;
import com.af.v4.system.common.jpa.service.SqlService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 限气表控制器
 * 用于查询返回字段，条件：f_State = '有效'
 */
@RestController
@RequestMapping("/limitgas")
public class LimitGasController {

    private static final Logger LOG = LoggerFactory.getLogger(LimitGasController.class);
    private final SqlService sqlService;

    public LimitGasController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 查询所有有效的限气记录
     * @return 返回有效状态的限气记录列表
     */
    @GetMapping("/valid")
    @ResponseBody
    public Map<String, Object> getValidLimitGas() {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始查询有效的限气记录");

            // 构建查询SQL（尝试不同的字段名）
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' ORDER BY f_operate_date DESC";

            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询有效限气记录", sql);

            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);

            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", convertLimitGasListToMapList(limitGasList));
            result.put("total", limitGasList.size());

            LOG.info("查询有效限气记录完成，共查询到 {} 条记录", limitGasList.size());

        } catch (Exception e) {
            LOG.error("查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }

        return result;
    }
}
