package com.af.v4.safecheck.controller;

import com.af.v4.safecheck.entity.LimitGas;
import com.af.v4.system.common.jpa.service.SqlService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.af.v4.safecheck.dto.RelieveLimitRequest;
import com.af.v4.safecheck.dto.AddLimitGasRequest;

/**
 * 限气表控制器
 * 用于查询返回字段，条件：f_State = '有效'
 */
@RestController
@RequestMapping("/limitgas")
public class LimitGasController {

    private static final Logger LOG = LoggerFactory.getLogger(LimitGasController.class);
    private final SqlService sqlService;

    public LimitGasController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 查询所有有效的限气记录
     * @return 返回有效状态的限气记录列表
     */
    @GetMapping("/valid")
    @ResponseBody
    public Map<String, Object> getValidLimitGas() {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始查询有效的限气记录");

            // 构建查询SQL（尝试不同的字段名）
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' ORDER BY f_operate_date DESC";

            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询有效限气记录", sql);

            result.put("code", 200);
            result.put("message", "查询成功");
        } catch (Exception e) {
            LOG.error("查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }

        return result;
    }

    /**
     * 解除限购接口
     * @param request 解除限购请求参数
     * @return 返回解除限购结果
     */
    @PostMapping("/cc")
    @ResponseBody
    public Map<String, Object> relieveLimit(@RequestBody RelieveLimitRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理解除限购请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());
            LOG.info("解除原因：{}", request.getRemove());
            LOG.info("其他原因：{}", request.getF_void_remarks());
            LOG.info("修改用户ID：{}", request.getF_modify_user_id());
            LOG.info("修改用户姓名：{}", request.getF_modify_user_name());
            LOG.info("修改时间：{}", request.getF_modify_date());
            LOG.info("限购记录ID：{}", request.getF_limit_id());
            LOG.info("限购类型：{}", request.getF_limit_style());
            LOG.info("限制周期：{}", request.getF_time_type());
            LOG.info("支付限制：{}", request.getF_limit_type());

            // 1. 先查询当前记录是否存在且状态为有效
            String queryLimitSql = "SELECT * FROM t_limit_gas WHERE id = " + request.getF_limit_id() + " AND f_State = '有效'";
            JSONArray queryResult = sqlService.querySQL("查询限购记录", queryLimitSql);

            if (queryResult.length() == 0) {
                result.put("code", 404);
                result.put("message", "未找到有效的限购记录，记录ID：" + request.getF_limit_id());
                result.put("success", false);
                LOG.warn("未找到有效的限购记录，ID：{}", request.getF_limit_id());
                return result;
            }

            LOG.info("找到有效限购记录，准备更新状态");

            // 2. 更新记录状态为无效
            String updateSql = "UPDATE t_limit_gas SET f_State = '无效' WHERE id = " + request.getF_limit_id();

            LOG.info("执行更新SQL：{}", updateSql);

            // 执行更新操作
            try {
                sqlService.querySQL("更新限购记录状态", updateSql);
                LOG.info("限购记录状态更新成功，ID：{}", request.getF_limit_id());
            } catch (Exception updateEx) {
                LOG.error("更新限购记录状态失败", updateEx);
                result.put("code", 500);
                result.put("message", "更新限购记录状态失败：" + updateEx.getMessage());
                result.put("success", false);
                return result;
            }

            // 3. 生成解除ID
            String relieveId = "relieve_" + UUID.randomUUID().toString().replace("-", "").substring(0, 6);

            // 4. 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("relieveId", relieveId);
            data.put("limitId", request.getF_limit_id());
            data.put("relieveTime", request.getF_modify_date());
            data.put("relieveUser", request.getF_modify_user_name());
            data.put("status", "已解除");

            result.put("code", 200);
            result.put("message", "解除限购成功");
            result.put("data", data);
            result.put("success", true);

            LOG.info("解除限购处理完成，解除ID：{}，限购记录ID：{}", relieveId, request.getF_limit_id());

        } catch (Exception e) {
            LOG.error("解除限购处理失败", e);
            result.put("code", 500);
            result.put("message", "解除限购失败：" + e.getMessage());
            result.put("success", false);
        }

        return result;
    }

    /**
     * 新增限购接口
     * @param request 新增限购请求参数
     * @return 返回新增限购结果
     */
    @PostMapping("/vv")
    @ResponseBody
    public Map<String, Object> addLimitGas(@RequestBody AddLimitGasRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理新增限购请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());
            LOG.info("限购类型：{}", request.getFLimitStyle());
            LOG.info("限制周期：{}", request.getFTimeType());
            LOG.info("支付限制：{}", request.getFLimitType());
            LOG.info("执行时间：{}", request.getFStartDate());
            LOG.info("单次限购：{}", request.getFTimeValue());
            LOG.info("支持第三方缴费：{}", request.getFThirdPay());
            LOG.info("限购次数：{}", request.getFLimitTimes());
            LOG.info("操作时间：{}", request.getFOperateDate());
            LOG.info("用户信息ID：{}", request.getFUserinfoId());
            LOG.info("资源用户ID：{}", request.getFResourcesId());
            LOG.info("资源用户名称：{}", request.getFResourcesName());

            // 生成限购ID
            String limitGasId = "limit_gas_" + String.format("%03d", (int)(Math.random() * 1000));

            // 生成当前时间
            LocalDateTime now = LocalDateTime.now();
            String createdAt = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("id", limitGasId);
            data.put("created_at", createdAt);

            result.put("success", true);
            result.put("message", "新增限购成功");
            result.put("data", data);
            result.put("timestamp", timestamp);

            LOG.info("新增限购处理完成，限购ID：{}", limitGasId);

        } catch (Exception e) {
            LOG.error("新增限购处理失败", e);
            result.put("success", false);
            result.put("message", "新增限购失败：" + e.getMessage());
            result.put("data", null);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        }

        return result;
    }
}
