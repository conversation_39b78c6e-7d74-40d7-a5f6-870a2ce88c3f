package com.af.v4.safecheck.controller;

import com.af.v4.safecheck.entity.LimitGas;
import com.af.v4.system.common.jpa.service.SqlService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 限气表控制器
 * 用于查询返回字段，条件：f_State = '有效'
 */
@RestController
@RequestMapping("/limitgas")
public class LimitGasController {

    private static final Logger LOG = LoggerFactory.getLogger(LimitGasController.class);
    private final SqlService sqlService;

    public LimitGasController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 查询所有有效的限气记录
     * @return 返回有效状态的限气记录列表
     */
    @GetMapping("/valid")
    public JSONObject getValidLimitGas() {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始查询有效的限气记录");
            
            // 构建查询SQL
            String sql = "SELECT * FROM t_limit_gas WHERE f_state = '有效' ORDER BY f_operate_date DESC";
            
            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询有效限气记录", sql);
            
            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", limitGasList);
            result.put("total", limitGasList.size());
            
            LOG.info("查询有效限气记录完成，共查询到 {} 条记录", limitGasList.size());
            
        } catch (Exception e) {
            LOG.error("查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    /**
     * 根据用户信息ID查询有效的限气记录
     * @param userinfoId 用户信息ID
     * @return 返回指定用户的有效限气记录
     */
    @GetMapping("/valid/user/{userinfoId}")
    public JSONObject getValidLimitGasByUserId(@PathVariable Integer userinfoId) {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始查询用户ID为 {} 的有效限气记录", userinfoId);
            
            // 构建查询SQL
            String sql = "SELECT * FROM t_limit_gas WHERE f_state = '有效' AND f_userinfo_id = " + userinfoId + " ORDER BY f_operate_date DESC";
            
            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询指定用户有效限气记录", sql);
            
            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", limitGasList);
            result.put("total", limitGasList.size());
            
            LOG.info("查询用户ID为 {} 的有效限气记录完成，共查询到 {} 条记录", userinfoId, limitGasList.size());
            
        } catch (Exception e) {
            LOG.error("查询用户ID为 {} 的有效限气记录失败", userinfoId, e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    /**
     * 分页查询有效的限气记录
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 返回分页的有效限气记录
     */
    @GetMapping("/valid/page")
    public JSONObject getValidLimitGasWithPagination(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始分页查询有效限气记录，页码：{}，每页大小：{}", page, size);
            
            // 构建查询SQL
            String sql = "SELECT * FROM t_limit_gas WHERE f_state = '有效' ORDER BY f_operate_date DESC";
            
            // 执行分页查询
            JSONArray queryResult = sqlService.query("分页查询有效限气记录", new JSONObject().put("sql", sql), page, size);
            
            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);
            
            // 查询总数
            String countSql = "SELECT COUNT(*) as total FROM t_limit_gas WHERE f_state = '有效'";
            JSONArray countResult = sqlService.querySQL("查询有效限气记录总数", countSql);
            int total = countResult.getJSONObject(0).getInt("total");
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", limitGasList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            LOG.info("分页查询有效限气记录完成，当前页：{}，每页大小：{}，总记录数：{}", page, size, total);
            
        } catch (Exception e) {
            LOG.error("分页查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    /**
     * 根据ID查询单个有效的限气记录
     * @param id 限气记录ID
     * @return 返回指定ID的有效限气记录
     */
    @GetMapping("/valid/{id}")
    public JSONObject getValidLimitGasById(@PathVariable Integer id) {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始查询ID为 {} 的有效限气记录", id);
            
            // 构建查询SQL
            String sql = "SELECT * FROM t_limit_gas WHERE f_state = '有效' AND id = " + id;
            
            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询指定ID有效限气记录", sql);
            
            if (queryResult.length() > 0) {
                // 转换为实体对象
                LimitGas limitGas = convertToLimitGas(queryResult.getJSONObject(0));
                
                result.put("code", 200);
                result.put("message", "查询成功");
                result.put("data", limitGas);
                
                LOG.info("查询ID为 {} 的有效限气记录成功", id);
            } else {
                result.put("code", 404);
                result.put("message", "未找到指定ID的有效限气记录");
                result.put("data", null);
                
                LOG.warn("未找到ID为 {} 的有效限气记录", id);
            }
            
        } catch (Exception e) {
            LOG.error("查询ID为 {} 的有效限气记录失败", id, e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", null);
        }
        
        return result;
    }

    /**
     * 将JSONArray转换为LimitGas对象列表
     * @param jsonArray JSON数组
     * @return LimitGas对象列表
     */
    private List<LimitGas> convertToLimitGasList(JSONArray jsonArray) {
        List<LimitGas> limitGasList = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            LimitGas limitGas = convertToLimitGas(jsonObject);
            limitGasList.add(limitGas);
        }
        return limitGasList;
    }

    /**
     * 将JSONObject转换为LimitGas对象
     * @param jsonObject JSON对象
     * @return LimitGas对象
     */
    private LimitGas convertToLimitGas(JSONObject jsonObject) {
        LimitGas limitGas = new LimitGas();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        try {
            // 设置基本字段
            limitGas.setId(jsonObject.optInt("id"));
            limitGas.setfThirdPay(jsonObject.optInt("f_third_pay"));
            limitGas.setfTimeType(jsonObject.optString("f_time_type"));
            limitGas.setfTimeValue(jsonObject.optString("f_time_value"));
            limitGas.setfLimitTimes(jsonObject.optString("f_limit_times"));
            limitGas.setfLimitType(jsonObject.optString("f_limit_type"));
            limitGas.setfLimitValue(jsonObject.optString("f_limit_value"));
            limitGas.setfPriceId(jsonObject.optInt("f_price_id"));
            limitGas.setfUserinfoId(jsonObject.optInt("f_userinfo_id"));
            limitGas.setfLimitAmount(jsonObject.optString("f_limit_amount"));
            limitGas.setfLimitStyle(jsonObject.optString("f_limit_style"));
            limitGas.setfState(jsonObject.optString("f_state"));
            limitGas.setfOperatePeople(jsonObject.optString("f_operate_people"));
            limitGas.setfCancelPeople(jsonObject.optString("f_cancel_people"));
            limitGas.setfOperatePeopleid(jsonObject.optString("f_operate_peopleid"));
            limitGas.setfOperateReason(jsonObject.optString("f_operate_reason"));
            limitGas.setfCancelPeopleid(jsonObject.optString("f_cancel_peopleid"));
            limitGas.setfCancelReason(jsonObject.optString("f_cancel_reason"));
            limitGas.setfLimitSource(jsonObject.optString("f_limit_source"));
            limitGas.setfLimitChannel(jsonObject.optString("f_limit_channel"));
            limitGas.setfLimitRecodId(jsonObject.optString("f_limit_recod_id"));
            limitGas.setfCancleRecordId(jsonObject.optString("f_cancle_record_id"));
            limitGas.setfBusiness(jsonObject.optString("f_business"));
            limitGas.setfLimitComments(jsonObject.optString("f_limit_comments"));
            
            // 处理日期字段
            String startDateStr = jsonObject.optString("f_start_date");
            if (startDateStr != null && !startDateStr.isEmpty() && !startDateStr.equals("null")) {
                limitGas.setfStartDate(dateFormat.parse(startDateStr));
            }
            
            String operateDateStr = jsonObject.optString("f_operate_date");
            if (operateDateStr != null && !operateDateStr.isEmpty() && !operateDateStr.equals("null")) {
                limitGas.setfOperateDate(dateFormat.parse(operateDateStr));
            }
            
            String cancleDateStr = jsonObject.optString("f_cancle_date");
            if (cancleDateStr != null && !cancleDateStr.isEmpty() && !cancleDateStr.equals("null")) {
                limitGas.setfCancleDate(dateFormat.parse(cancleDateStr));
            }
            
        } catch (Exception e) {
            LOG.warn("转换JSONObject到LimitGas对象时出现异常", e);
        }
        
        return limitGas;
    }
}
