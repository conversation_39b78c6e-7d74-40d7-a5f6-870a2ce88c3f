package com.af.v4.safecheck.controller;

import com.af.v4.safecheck.entity.LimitGas;
import com.af.v4.system.common.jpa.service.SqlService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 限气表控制器
 * 用于查询返回字段，条件：f_State = '有效'
 */
@RestController
@RequestMapping("/limitgas")
public class LimitGasController {

    private static final Logger LOG = LoggerFactory.getLogger(LimitGasController.class);
    private final SqlService sqlService;

    public LimitGasController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 查询所有有效的限气记录
     * @return 返回有效状态的限气记录列表
     */
    @GetMapping("/valid")
    public JSONObject getValidLimitGas() {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始查询有效的限气记录");
            
            // 构建查询SQL（尝试不同的字段名）
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' ORDER BY f_operate_date DESC";
            
            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询有效限气记录", sql);
            
            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", convertLimitGasListToJsonArray(limitGasList));
            result.put("total", limitGasList.size());
            
            LOG.info("查询有效限气记录完成，共查询到 {} 条记录", limitGasList.size());
            
        } catch (Exception e) {
            LOG.error("查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    /**
     * 根据用户信息ID查询有效的限气记录
     * @param userinfoId 用户信息ID
     * @return 返回指定用户的有效限气记录
     */
    @GetMapping("/valid/user/{userinfoId}")
    public JSONObject getValidLimitGasByUserId(@PathVariable Integer userinfoId) {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始查询用户ID为 {} 的有效限气记录", userinfoId);
            
            // 构建查询SQL
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' AND f_userinfo_id = " + userinfoId + " ORDER BY f_operate_date DESC";
            
            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询指定用户有效限气记录", sql);
            
            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", convertLimitGasListToJsonArray(limitGasList));
            result.put("total", limitGasList.size());
            
            LOG.info("查询用户ID为 {} 的有效限气记录完成，共查询到 {} 条记录", userinfoId, limitGasList.size());
            
        } catch (Exception e) {
            LOG.error("查询用户ID为 {} 的有效限气记录失败", userinfoId, e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    /**
     * 分页查询有效的限气记录
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 返回分页的有效限气记录
     */
    @GetMapping("/valid/page")
    public JSONObject getValidLimitGasWithPagination(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始分页查询有效限气记录，页码：{}，每页大小：{}", page, size);
            
            // 构建查询SQL（不使用分页，直接查询所有数据然后在代码中分页）
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' ORDER BY f_operate_date DESC";

            // 执行查询
            JSONArray allResults = sqlService.querySQL("查询有效限气记录", sql);

            // 手动分页
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, allResults.length());

            JSONArray queryResult = new JSONArray();
            for (int i = startIndex; i < endIndex; i++) {
                queryResult.put(allResults.getJSONObject(i));
            }
            
            // 转换为实体对象列表
            List<LimitGas> limitGasList = convertToLimitGasList(queryResult);

            // 总数就是所有结果的长度
            int total = allResults.length();
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", convertLimitGasListToJsonArray(limitGasList));
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            LOG.info("分页查询有效限气记录完成，当前页：{}，每页大小：{}，总记录数：{}", page, size, total);
            
        } catch (Exception e) {
            LOG.error("分页查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    /**
     * 根据ID查询单个有效的限气记录
     * @param id 限气记录ID
     * @return 返回指定ID的有效限气记录
     */
    @GetMapping("/valid/{id}")
    public JSONObject getValidLimitGasById(@PathVariable Integer id) {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始查询ID为 {} 的有效限气记录", id);
            
            // 构建查询SQL
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' AND id = " + id;
            
            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询指定ID有效限气记录", sql);
            
            if (queryResult.length() > 0) {
                // 转换为实体对象
                LimitGas limitGas = convertToLimitGas(queryResult.getJSONObject(0));
                
                result.put("code", 200);
                result.put("message", "查询成功");
                result.put("data", convertLimitGasToJsonObject(limitGas));
                
                LOG.info("查询ID为 {} 的有效限气记录成功", id);
            } else {
                result.put("code", 404);
                result.put("message", "未找到指定ID的有效限气记录");
                result.put("data", "");
                
                LOG.warn("未找到ID为 {} 的有效限气记录", id);
            }
            
        } catch (Exception e) {
            LOG.error("查询ID为 {} 的有效限气记录失败", id, e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", "");
        }
        
        return result;
    }

    /**
     * 测试数据库连接和表是否存在
     * @return 返回测试结果
     */
    @GetMapping("/test")
    public JSONObject testConnection() {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始测试数据库连接和表结构");

            // 测试表是否存在
            String testSql = "SELECT COUNT(*) as count FROM t_limit_gas";
            JSONArray testResult = sqlService.querySQL("测试表连接", testSql);

            // 获取总记录数，处理可能的字段名差异
            JSONObject firstResult = testResult.getJSONObject(0);
            int totalCount = 0;
            if (firstResult.has("count")) {
                totalCount = firstResult.getInt("count");
            } else if (firstResult.has("COUNT(*)")) {
                totalCount = firstResult.getInt("COUNT(*)");
            } else {
                // 如果都没有，取第一个数值字段
                String[] keys = JSONObject.getNames(firstResult);
                if (keys != null && keys.length > 0) {
                    totalCount = firstResult.getInt(keys[0]);
                }
            }

            result.put("code", 200);
            result.put("message", "数据库连接正常");
            result.put("totalRecords", totalCount);

            // 测试查询有效记录数量
            String validSql = "SELECT COUNT(*) as validCount FROM t_limit_gas WHERE f_state = '有效'";
            JSONArray validResult = sqlService.querySQL("测试有效记录", validSql);

            // 获取有效记录数，处理可能的字段名差异
            JSONObject validFirstResult = validResult.getJSONObject(0);
            int validCount = 0;
            if (validFirstResult.has("validCount")) {
                validCount = validFirstResult.getInt("validCount");
            } else if (validFirstResult.has("COUNT(*)")) {
                validCount = validFirstResult.getInt("COUNT(*)");
            } else {
                // 如果都没有，取第一个数值字段
                String[] validKeys = JSONObject.getNames(validFirstResult);
                if (validKeys != null && validKeys.length > 0) {
                    validCount = validFirstResult.getInt(validKeys[0]);
                }
            }

            result.put("validRecords", validCount);

            // 添加调试信息
            result.put("debug", new JSONObject()
                .put("totalQueryResult", testResult.toString())
                .put("validQueryResult", validResult.toString())
                .put("totalResultKeys", JSONObject.getNames(firstResult))
                .put("validResultKeys", JSONObject.getNames(validFirstResult))
            );

            LOG.info("数据库测试完成，总记录数：{}，有效记录数：{}", totalCount, validCount);

        } catch (Exception e) {
            LOG.error("数据库连接测试失败", e);
            result.put("code", 500);
            result.put("message", "数据库连接失败：" + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }

    /**
     * 简单的数据库连接测试
     * @return 返回测试结果
     */
    @GetMapping("/test-simple")
    public JSONObject testSimpleConnection() {
        JSONObject result = new JSONObject();
        try {
            LOG.info("开始简单数据库连接测试");

            // 步骤1：测试表是否存在
            result.put("step1", "测试表是否存在");
            try {
                String checkTableSql = "SELECT COUNT(*) FROM t_limit_gas";
                JSONArray tableResult = sqlService.querySQL("检查表存在", checkTableSql);
                result.put("tableExists", true);
                result.put("tableCheckResult", tableResult.toString());

                // 步骤2：查询前5条数据（避免数据量过大）
                String allDataSql = "SELECT TOP 5 * FROM t_limit_gas";
                JSONArray allResult = sqlService.querySQL("查询前5条数据", allDataSql);
                result.put("sampleRecords", allResult.length());
                result.put("sampleDataResult", allResult.toString());

                // 步骤3：查询总记录数
                String countSql = "SELECT COUNT(*) as total_count FROM t_limit_gas";
                JSONArray countResult = sqlService.querySQL("查询总数", countSql);
                result.put("totalCountResult", countResult.toString());

                // 步骤4：查询所有状态值（尝试不同的字段名）
                String statesSql1 = "SELECT DISTINCT f_state FROM t_limit_gas WHERE f_state IS NOT NULL";
                JSONArray statesResult1 = sqlService.querySQL("查询状态f_state", statesSql1);
                result.put("allStates_f_state", statesResult1.toString());

                String statesSql2 = "SELECT DISTINCT f_State FROM t_limit_gas WHERE f_State IS NOT NULL";
                JSONArray statesResult2 = sqlService.querySQL("查询状态f_State", statesSql2);
                result.put("allStates_f_State", statesResult2.toString());

                // 步骤5：查询有效数据（尝试不同的字段名和值）
                String validDataSql1 = "SELECT COUNT(*) as valid_count FROM t_limit_gas WHERE f_state = '有效'";
                JSONArray validResult1 = sqlService.querySQL("查询有效数据1", validDataSql1);
                result.put("validRecords_f_state", validResult1.toString());

                String validDataSql2 = "SELECT COUNT(*) as valid_count FROM t_limit_gas WHERE f_State = '有效'";
                JSONArray validResult2 = sqlService.querySQL("查询有效数据2", validDataSql2);
                result.put("validRecords_f_State", validResult2.toString());

                if (allResult.length() > 0) {
                    JSONObject firstRecord = allResult.getJSONObject(0);
                    result.put("sampleRecord", firstRecord);
                    result.put("availableFields", JSONObject.getNames(firstRecord));
                }

            } catch (Exception tableEx) {
                result.put("tableExists", false);
                result.put("tableError", tableEx.getMessage());

                // 如果表不存在，尝试查看所有表
                try {
                    String showTablesSql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
                    JSONArray tablesResult = sqlService.querySQL("查看所有表", showTablesSql);
                    result.put("allTables", tablesResult.toString());
                } catch (Exception ex2) {
                    result.put("showTablesError", ex2.getMessage());
                }
            }

            result.put("code", 200);
            result.put("message", "测试完成");

            LOG.info("数据库连接测试完成");

        } catch (Exception e) {
            LOG.error("数据库连接测试失败", e);
            result.put("code", 500);
            result.put("message", "测试失败：" + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            result.put("stackTrace", e.toString());
        }

        return result;
    }

    /**
     * 最基础的数据库测试
     * @return 返回测试结果
     */
    @GetMapping("/test-basic")
    @ResponseBody
    public Map<String, Object> testBasicConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始最基础的数据库测试");

            // 测试SqlService的不同方法
            result.put("step", "测试SqlService各种方法");

            // 方法1：最基础的查询
            try {
                String basicSql = "SELECT GETDATE() as current_time";
                JSONArray basicResult = sqlService.querySQL("基础时间查询", basicSql);
                result.put("method1_querySQL", basicResult.toString());
                result.put("method1_length", basicResult.length());
                result.put("method1_type", basicResult.getClass().getSimpleName());
            } catch (Exception e1) {
                result.put("method1_error", e1.getMessage());
            }

            // 方法2：尝试不同的SQL
            try {
                String simpleSql = "SELECT 1 as test_number, 'test' as test_string";
                JSONArray simpleResult = sqlService.querySQL("简单查询", simpleSql);
                result.put("method2_querySQL", simpleResult.toString());
                result.put("method2_length", simpleResult.length());
            } catch (Exception e2) {
                result.put("method2_error", e2.getMessage());
            }

            // 方法3：尝试查询系统表
            try {
                String systemSql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
                JSONArray systemResult = sqlService.querySQL("查询系统表", systemSql);
                result.put("method3_querySQL", systemResult.toString());
                result.put("method3_length", systemResult.length());
            } catch (Exception e3) {
                result.put("method3_error", e3.getMessage());
            }

            // 方法4：尝试查询我们的目标表
            try {
                String targetSql = "SELECT TOP 1 * FROM t_limit_gas";
                JSONArray targetResult = sqlService.querySQL("查询目标表", targetSql);
                result.put("method4_querySQL", targetResult.toString());
                result.put("method4_length", targetResult.length());
                if (targetResult.length() > 0) {
                    result.put("method4_firstRecord", targetResult.get(0).toString());
                    result.put("method4_firstRecordType", targetResult.get(0).getClass().getSimpleName());
                }
            } catch (Exception e4) {
                result.put("method4_error", e4.getMessage());
            }

            result.put("code", 200);
            result.put("message", "测试完成");

            LOG.info("基础数据库测试完成");

        } catch (Exception e) {
            LOG.error("基础数据库测试失败", e);
            result.put("code", 500);
            result.put("message", "测试失败：" + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            result.put("stackTrace", e.toString());
        }

        return result;
    }

    /**
     * 测试SqlService状态
     * @return 返回测试结果
     */
    @GetMapping("/test-service")
    @ResponseBody
    public Map<String, Object> testSqlService() {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始测试SqlService状态");

            result.put("sqlServiceExists", sqlService != null);
            if (sqlService != null) {
                result.put("sqlServiceClass", sqlService.getClass().getName());
                result.put("sqlServiceToString", sqlService.toString());
            }

            // 尝试调用最简单的方法
            try {
                // 如果SqlService有其他方法，我们可以尝试
                result.put("attemptQuery", "尝试执行查询");

                // 直接返回一个测试结果，不依赖数据库
                List<Map<String, Object>> testList = new ArrayList<>();
                Map<String, Object> testItem = new HashMap<>();
                testItem.put("test", "value");
                testList.add(testItem);
                result.put("manualTestArray", testList);

            } catch (Exception serviceEx) {
                result.put("serviceError", serviceEx.getMessage());
            }

            result.put("code", 200);
            result.put("message", "SqlService测试完成");

        } catch (Exception e) {
            LOG.error("SqlService测试失败", e);
            result.put("code", 500);
            result.put("message", "测试失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 最简单的测试接口
     * @return 返回测试结果
     */
    @GetMapping("/test-hello")
    @ResponseBody
    public Map<String, Object> testHello() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "Hello World");
        result.put("timestamp", System.currentTimeMillis());
        result.put("sqlServiceExists", sqlService != null);

        if (sqlService != null) {
            result.put("sqlServiceClass", sqlService.getClass().getSimpleName());
        }

        return result;
    }

    /**
     * 将JSONArray转换为LimitGas对象列表
     * @param jsonArray JSON数组
     * @return LimitGas对象列表
     */
    private List<LimitGas> convertToLimitGasList(JSONArray jsonArray) {
        List<LimitGas> limitGasList = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            LimitGas limitGas = convertToLimitGas(jsonObject);
            limitGasList.add(limitGas);
        }
        return limitGasList;
    }

    /**
     * 将JSONObject转换为LimitGas对象
     * @param jsonObject JSON对象
     * @return LimitGas对象
     */
    private LimitGas convertToLimitGas(JSONObject jsonObject) {
        LimitGas limitGas = new LimitGas();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        try {
            // 设置基本字段
            limitGas.setId(jsonObject.optInt("id"));
            limitGas.setfThirdPay(jsonObject.optInt("f_third_pay"));
            limitGas.setfTimeType(jsonObject.optString("f_time_type"));
            limitGas.setfTimeValue(jsonObject.optString("f_time_value"));
            limitGas.setfLimitTimes(jsonObject.optString("f_limit_times"));
            limitGas.setfLimitType(jsonObject.optString("f_limit_type"));
            limitGas.setfLimitValue(jsonObject.optString("f_limit_value"));
            limitGas.setfPriceId(jsonObject.optInt("f_price_id"));
            limitGas.setfUserinfoId(jsonObject.optInt("f_userinfo_id"));
            limitGas.setfLimitAmount(jsonObject.optString("f_limit_amount"));
            limitGas.setfLimitStyle(jsonObject.optString("f_limit_style"));
            limitGas.setfState(jsonObject.optString("f_state"));
            limitGas.setfOperatePeople(jsonObject.optString("f_operate_people"));
            limitGas.setfCancelPeople(jsonObject.optString("f_cancel_people"));
            limitGas.setfOperatePeopleid(jsonObject.optString("f_operate_peopleid"));
            limitGas.setfOperateReason(jsonObject.optString("f_operate_reason"));
            limitGas.setfCancelPeopleid(jsonObject.optString("f_cancel_peopleid"));
            limitGas.setfCancelReason(jsonObject.optString("f_cancel_reason"));
            limitGas.setfLimitSource(jsonObject.optString("f_limit_source"));
            limitGas.setfLimitChannel(jsonObject.optString("f_limit_channel"));
            limitGas.setfLimitRecodId(jsonObject.optString("f_limit_recod_id"));
            limitGas.setfCancleRecordId(jsonObject.optString("f_cancle_record_id"));
            limitGas.setfBusiness(jsonObject.optString("f_business"));
            limitGas.setfLimitComments(jsonObject.optString("f_limit_comments"));
            
            // 处理日期字段
            String startDateStr = jsonObject.optString("f_start_date");
            if (startDateStr != null && !startDateStr.isEmpty() && !startDateStr.equals("null")) {
                limitGas.setfStartDate(dateFormat.parse(startDateStr));
            }
            
            String operateDateStr = jsonObject.optString("f_operate_date");
            if (operateDateStr != null && !operateDateStr.isEmpty() && !operateDateStr.equals("null")) {
                limitGas.setfOperateDate(dateFormat.parse(operateDateStr));
            }
            
            String cancleDateStr = jsonObject.optString("f_cancle_date");
            if (cancleDateStr != null && !cancleDateStr.isEmpty() && !cancleDateStr.equals("null")) {
                limitGas.setfCancleDate(dateFormat.parse(cancleDateStr));
            }
            
        } catch (Exception e) {
            LOG.warn("转换JSONObject到LimitGas对象时出现异常", e);
        }
        
        return limitGas;
    }

    /**
     * 将LimitGas对象列表转换为JSONArray
     * @param limitGasList LimitGas对象列表
     * @return JSONArray
     */
    private JSONArray convertLimitGasListToJsonArray(List<LimitGas> limitGasList) {
        JSONArray jsonArray = new JSONArray();
        for (LimitGas limitGas : limitGasList) {
            jsonArray.put(convertLimitGasToJsonObject(limitGas));
        }
        return jsonArray;
    }

    /**
     * 将LimitGas对象转换为JSONObject
     * @param limitGas LimitGas对象
     * @return JSONObject
     */
    private JSONObject convertLimitGasToJsonObject(LimitGas limitGas) {
        JSONObject jsonObject = new JSONObject();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        jsonObject.put("id", limitGas.getId());
        jsonObject.put("fThirdPay", limitGas.getfThirdPay());
        jsonObject.put("fTimeType", limitGas.getfTimeType());
        jsonObject.put("fTimeValue", limitGas.getfTimeValue());
        jsonObject.put("fLimitTimes", limitGas.getfLimitTimes());
        jsonObject.put("fLimitType", limitGas.getfLimitType());
        jsonObject.put("fLimitValue", limitGas.getfLimitValue());
        jsonObject.put("fPriceId", limitGas.getfPriceId());
        jsonObject.put("fUserinfoId", limitGas.getfUserinfoId());
        jsonObject.put("fLimitAmount", limitGas.getfLimitAmount());
        jsonObject.put("fLimitStyle", limitGas.getfLimitStyle());
        jsonObject.put("fState", limitGas.getfState());
        jsonObject.put("fOperatePeople", limitGas.getfOperatePeople());
        jsonObject.put("fCancelPeople", limitGas.getfCancelPeople());
        jsonObject.put("fOperatePeopleid", limitGas.getfOperatePeopleid());
        jsonObject.put("fOperateReason", limitGas.getfOperateReason());
        jsonObject.put("fCancelPeopleid", limitGas.getfCancelPeopleid());
        jsonObject.put("fCancelReason", limitGas.getfCancelReason());
        jsonObject.put("fLimitSource", limitGas.getfLimitSource());
        jsonObject.put("fLimitChannel", limitGas.getfLimitChannel());
        jsonObject.put("fLimitRecodId", limitGas.getfLimitRecodId());
        jsonObject.put("fCancleRecordId", limitGas.getfCancleRecordId());
        jsonObject.put("fBusiness", limitGas.getfBusiness());
        jsonObject.put("fLimitComments", limitGas.getfLimitComments());

        // 处理日期字段
        if (limitGas.getfStartDate() != null) {
            jsonObject.put("fStartDate", dateFormat.format(limitGas.getfStartDate()));
        }
        if (limitGas.getfOperateDate() != null) {
            jsonObject.put("fOperateDate", dateFormat.format(limitGas.getfOperateDate()));
        }
        if (limitGas.getfCancleDate() != null) {
            jsonObject.put("fCancleDate", dateFormat.format(limitGas.getfCancleDate()));
        }

        return jsonObject;
    }
}
