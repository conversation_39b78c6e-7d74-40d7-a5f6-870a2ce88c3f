package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.json.JSONArray;
import org.springframework.stereotype.Component;

/**
 * com.aote.timer
 *
 * <AUTHOR>
 * @creater 2020-07-07 15:52
 */
@Component
public class PlanTimer {
    private final LogicService logicService;
    private String logicName;
    private String logicPath;

    public PlanTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    public String getLogicName() {
        return logicName;
    }

    public void setLogicName(String logicName) {
        this.logicName = logicName;
    }

    public String getLogicPath() {
        return logicPath;
    }

    public void setLogicPath(String logicPath) {
        this.logicPath = logicPath;
    }

    public void queryAndCreatePlan() {
        selectPlan();
    }

    public void selectPlan() {
        JSONArray query = new JSONArray(logicService.run("onlyQuery", "{data:{}}"));

        for (int i = 0; i < query.length(); i++) {
            logicService.run("TimeCreatePlan", "{data:" + query.get(i) + "}");
        }
    }
}
