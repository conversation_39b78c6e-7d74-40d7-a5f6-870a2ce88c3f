package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * com.aote.timer
 *
 * <AUTHOR>
 * @creater 2020-07-07 15:52
 */
@Component
public class CommonTimer {
    private final LogicService logicService;
    private String logicName;

    public CommonTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    public void runLogic() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("aoteEncrypt", "AES");
        jsonObject.put("data", "rp5PoNj4shopCFpZwfrm3w==");
        logicService.run(logicName, jsonObject);
    }

    public void setLogicName(String logicName) {
        this.logicName = logicName;
    }
}
