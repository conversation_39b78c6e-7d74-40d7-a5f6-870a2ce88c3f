package com.af.v4.safecheck.v3.rs;

import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 对工单进行处理的插件，前后台都用
 */
public class ServicePlugin {
    private static final Logger log = LoggerFactory.getLogger(ServicePlugin.class);

    // 根据前台传过来的一批工单编号，以及查询到的待办工单内容，返回新增工单/删除工单/修改工单结果
    // androids: 安卓端未完成的工单列表，JSON串, 格式: [{id: 安卓端id, id_back: pc端工单 id,
    // service_version: 版本号}]
    // pcs: pc端查找到的未处理完的工单，JSON串， 格式：[{完整工单内容，service_version:版本号, 故障信息}]
    // 返回结果: json对象，格式 {inserts: [新增工单], deletes: [{id_back: 工单 id}], modifies:
    // [修改工单]}
    public JSONObject getChanged(JSONArray androids, JSONArray pcs) {
        JSONArray inserts = new JSONArray();
        JSONArray deletes = new JSONArray();
        JSONArray modifies = new JSONArray();
        JSONObject result = new JSONObject();
        JSONObject row;

        result.put("inserts", inserts);
        result.put("deletes", deletes);
        result.put("modifies", modifies);

        JSONArray itemDe = new JSONArray();
        JSONArray itemIn = new JSONArray();
        JSONArray itemMo = new JSONArray();

        result.put("itemDe", itemDe);
        result.put("itemIn", itemIn);
        result.put("itemMo", itemMo);

        JSONArray noDeletes = new JSONArray();
        for (Object pcObj : pcs) {
            // 如果pc端工单不包含在安卓端，新增
            JSONObject pc = (JSONObject) pcObj;
            JSONObject android = this.getService(androids, "id", pc, "id");
            if (android == null) {
                inserts.put(pc);
            } else {
                noDeletes.put(android);
                JSONArray androidItems = android.getJSONArray("f_items");
                for (Object anItem : androidItems) {
                    JSONObject andTtem = (JSONObject) anItem;
                    //对比手机端拿到的安检项是否存在与
                    JSONObject pcsItem = this.getService(pc.getJSONArray("f_items"), "id", andTtem, "id");
                    if (pcsItem == null) {
                        itemDe.put(andTtem);
                    }
                }

                // 如果计划项发生变化，添加到修改里
                JSONArray items = pc.getJSONArray("f_items");
                for (Object item : items) {
                    JSONObject pcItem = (JSONObject) item;
                    //对比手机端与pc端安检项
                    JSONObject androidItem = this.getService(android.getJSONArray("f_items"), "id",
                            pcItem, "id");
                    if (androidItem == null) {
                        // if(!"已检".equals(pcItem.getString("f_state"))){   暂时去掉这个判断
                        //获取手机端新增安检项
                        itemIn.put(pcItem);
                        //}
                    } else {
                        JSONObject PItem = this.getVersion(pc.getJSONArray("f_items"), "id", androidItem, "id");
                        if (PItem != null) {
                            itemMo.put(PItem);
                        }
                    }
                }
            }
        }
        // 安卓端工单去掉pc端已包含的，就是要删除的
        for (Object o : androids) {
            JSONObject android = (JSONObject) o;
            JSONObject json = this.getService(noDeletes, "id", android, "id");
            if (json == null) {
                row = new JSONObject();
                row.put("id", android.getString("id"));
                deletes.put(row);
            }
        }
        return result;
    }

    /**
     * 获取共享计划中的变更内容
     */
    public JSONObject getPlanItemChanged(JSONArray androids, JSONArray pcs) {
        JSONObject result = new JSONObject();
        JSONArray mods = new JSONArray();
        result.put("mods", mods);
        for (Object o : androids) {
            JSONObject android = (JSONObject) o;
            JSONObject json = this.getService(pcs, "id", android, "id");
            if (json != null) {
                mods.put(json);
            }
        }
        return result;
    }

    /**
     * 获取安检计划中所有计划项
     */
    public JSONArray getFailures(JSONArray services) {
        JSONArray result = new JSONArray();
        for (Object obj : services) {
            JSONObject service = (JSONObject) obj;
            if (service.has("f_items")) {
                JSONArray items = service.getJSONArray("f_items");
                for (Object o : items) {
                    JSONObject item = (JSONObject) o;
                    result.put(item);
                }
            }
        }
        return result;
    }

    /**
     * item版本改变的Item
     */
    private JSONObject getVersion(JSONArray array, String arrayId, JSONObject json, String jsonId) {
        for (Object o : array) {
            JSONObject otherJson = (JSONObject) o;
            if (otherJson.getString(arrayId).equals(json.getString(jsonId))) {
                if (otherJson.getInt("version") > json.getInt("version")) {
                    return otherJson;
                }
            }
        }
        return null;
    }

    /**
     * 从工单集合中，获取与给定工单id相同的工单
     *
     * @param array   工单集合
     * @param arrayId 工单集合里的工单id名
     * @param json    要查看的工单
     * @param jsonId  要查看的工单的id名
     * @return 工单集合里的工单，没有，返回空
     */
    private JSONObject getService(JSONArray array, String arrayId,
                                  JSONObject json, String jsonId) {
        for (Object o : array) {
            JSONObject otherJson = (JSONObject) o;
            if (otherJson.getString(arrayId).equals(json.getString(jsonId))) {
                return otherJson;
            }
        }
        return null;
    }


    public JSONArray mergedArray(JSONArray array1, JSONArray array2) {
        JSONArray mergedArray = new JSONArray();
        for (int i = 0; i < array1.length(); i++) {
            mergedArray.put(array1.get(i));
        }
        for (int i = 0; i < array2.length(); i++) {
            mergedArray.put(array2.get(i));
        }
        return mergedArray;
    }
}
