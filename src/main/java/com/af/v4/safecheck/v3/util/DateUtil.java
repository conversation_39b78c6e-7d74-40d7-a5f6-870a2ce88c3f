package com.af.v4.safecheck.v3.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Author: 作者名
 * @Date: 2024/4/9
 */

public class DateUtil {
    //加
    public static String getNowAdd(String type, String num) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance(); // 获取当前日期时间的Calendar对象
        String time;

        if ("年".equals(type)) {
            calendar.add(Calendar.YEAR, Integer.parseInt(num)); // 在当前年份上增加num年
        } else if ("月".equals(type)) {
            calendar.add(Calendar.MONTH, Integer.parseInt(num)); // 在当前月份上增加num月
        } else if ("日".equals(type)) {
            calendar.add(Calendar.DAY_OF_MONTH, Integer.parseInt(num)); // 在当前日期上增加num天
        }

        Date newDate = calendar.getTime(); // 获取修改后的Date对象
        time = formatter.format(newDate); // 格式化日期为字符串

        return time;
    }
}
