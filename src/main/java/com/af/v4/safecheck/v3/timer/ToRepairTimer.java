package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.springframework.stereotype.Component;

/**
 * com.aote.timer
 *
 * <AUTHOR>
 * @creater 2020-09-17 15:52
 * 定时查询未成功发送维修单的安检单，进行补发
 */
@Component
public class ToRepairTimer {
    private final LogicService logicService;

    public ToRepairTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    public void timeOutToRepair() throws Exception {
        logicService.run("rc_timeOutToRepair", "{data:{}}");
    }
}
