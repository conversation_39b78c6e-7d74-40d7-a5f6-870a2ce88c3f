package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Objects;

/**
 * com.aote.timer
 *
 * <AUTHOR>
 * @creater 2020-07-07 15:52
 */
@Component
@Transactional(readOnly = true)
public class ChangeUserFilesTimer {

    static Logger log = LoggerFactory.getLogger(LogicService.class);
    private final LogicService logicService;
    private String logicName;
    private String logicPath;

    public ChangeUserFilesTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    public static String readFileContent(String fileName) {
        File file = new File(fileName);
        StringBuilder sbf = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                sbf.append(tempStr);
            }
            return sbf.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public String getLogicName() {
        return logicName;
    }

    public void setLogicName(String logicName) {
        this.logicName = logicName;
    }

    public String getLogicPath() {
        return logicPath;
    }

    public void setLogicPath(String logicPath) {
        this.logicPath = logicPath;
    }

    public void checkChangeUserFiles() throws Exception {
        changeFiles();
        log.info("定时器执行完成++++++++++++++++++++");
    }

    public void changeFiles() {
        String s = readFileContent(Objects.requireNonNull(ChangeUserFilesTimer.class.getResource("/")).getPath() + "changeFileConfig.json");
        JSONArray json = new JSONArray(s);
        json.forEach(res -> {
            logicService.run("AutoChangeUserFiles", res.toString());
        });
    }
}
