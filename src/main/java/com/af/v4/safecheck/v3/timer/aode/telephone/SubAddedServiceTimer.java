package com.af.v4.safecheck.v3.timer.aode.telephone;

import com.af.v4.system.common.logic.service.LogicService;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

@Component
public class SubAddedServiceTimer implements BasicProcessor {


    private final LogicService logicService;

    public SubAddedServiceTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        // 在线日志功能，可以直接在控制台查看任务日志，非常便捷
        OmsLogger omsLogger = context.getOmsLogger();
        omsLogger.info("置换增值业务数据推送", context.getJobParams());
        this.logicService.run("SubAddedServicesSync", "{data:{}}");
        return new ProcessResult(true, "任务执行成功，实例ID: "+ context.getInstanceId());
    }

}
