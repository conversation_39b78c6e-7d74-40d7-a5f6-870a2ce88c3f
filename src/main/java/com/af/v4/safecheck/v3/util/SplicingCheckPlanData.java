package com.af.v4.safecheck.v3.util;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class SplicingCheckPlanData {
    //替代path将两个父子查询的结果组合成一条
    public static JSONArray splicingData(JSONArray item,JSONArray meter){
        if(item.length()==0 || meter.length()==0){
            return item;
        }
        JSONArray result=new JSONArray();
        JSONObject obj=null;
        JSONObject obj1=null;
        for(int i=0;i<item.length();i++){
            obj=(JSONObject)item.get(i);
            obj.put("f_check_version",obj.get("check_version"));
            boolean hasMeter = false;
            for(int j=0;j<meter.length();j++){
                obj1=(JSONObject)meter.get(j);
                if(obj.getString("id").equals(obj1.getString("f_plan_item_id"))){
                    hasMeter = true;
                    obj.put("f_plan_meters",obj1);
                    result.put(obj);
                    meter.remove(j);
                    obj=null;
                    obj1=null;
                    break;
                }
            }
            // meter表数据目前从最新档案中在线取，计划中拿不到可能是因为预备户等原因。所以当计划生成时没有meter就跳过了
            if(!hasMeter){
                result.put(obj);
            }
        }
        return result;
    }

    /**
     *
     * @param sourceJsonArray 主数组
     * @param sourceKey 主数组中元素主键
     * @param insKey 主数组元素中需要生成的新组数的keyname
     * @param targetJsonArray 子数组
     * @param targetKey 子数组中元素关联的外键
     * @return 返回合并后的sourceJsonArray
     */
    public JSONArray mergeJsonArrays(JSONArray sourceJsonArray, String sourceKey, String insKey, JSONArray targetJsonArray, String targetKey) {
        // 使用 HashMap 来存储子表记录，键是 targetKey，值是该 targetKey 对应的所有子记录数组
        Map<String, JSONArray> subRecordsMap = new HashMap<>();
        // 预处理子表，构建 HashMap
        for (int i = 0; i < targetJsonArray.length(); i++) {
            JSONObject subRecord = targetJsonArray.getJSONObject(i);
            String deviceId = subRecord.get(targetKey).toString();
            JSONArray subRecordsArray = subRecordsMap.getOrDefault(deviceId, new JSONArray());
            subRecordsArray.put(subRecord);
            subRecordsMap.put(deviceId, subRecordsArray);
        }
        JSONArray mergedJsonArray = new JSONArray();
        // 遍历主表，并将匹配的子记录添加到主记录中
        for (int i = 0; i < sourceJsonArray.length(); i++) {
            JSONObject mainRecord = sourceJsonArray.getJSONObject(i);
            String mainId = mainRecord.get(sourceKey).toString();
            // 从 HashMap 中获取匹配的子记录数组
            JSONArray subRecordsArray = subRecordsMap.getOrDefault(mainId, new JSONArray());
            // 将子记录数组添加到主记录中
            mainRecord.put(insKey, subRecordsArray);
            // 将更新后的主记录添加到合并后的 JSONArray 中
            mergedJsonArray.put(mainRecord);
        }
        return mergedJsonArray;
    }
}
