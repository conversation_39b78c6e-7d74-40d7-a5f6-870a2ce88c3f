package com.af.v4.safecheck.v3.plugins;

import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Properties;
import java.util.UUID;


/**
 * 张俊杰
 */
public class LocationFileJsonUpload {
    static Logger log = LoggerFactory.getLogger(LocationFileJsonUpload.class);

    public LocationFileJsonUpload() {
    }

    public static String uploadDataToJsonFile(JSONArray dataJson) throws IOException {
        String context = dataJson.toString();
        String filePath = getPath("uploadDataToJsonFilePath");
        filePath = getRealPath(filePath) + File.separator + UUID.randomUUID().toString().replace("-", "") + ".json";
        log.info("将定位数据转为JSON保存到文件夹的位置为{}", filePath);
        File fileFile = new File(filePath);
        if (!fileFile.getParentFile().exists()) {
            fileFile.getParentFile().mkdirs();
        }
        fileFile.createNewFile();
        try (Writer write = new OutputStreamWriter(new FileOutputStream(fileFile), StandardCharsets.UTF_8)) {
            write.write(context);
            write.flush();
            return filePath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static JSONArray ReadLocationToFileJson(String absolutePath) {
        StringBuilder lastStr = new StringBuilder();
        File file = new File(absolutePath);
        JSONArray endResult = new JSONArray();

        try (FileInputStream in = new FileInputStream(file);
             BufferedReader reader = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8))) {
            String tempString;
            while ((tempString = reader.readLine()) != null) {
                lastStr.append(tempString);
            }
            JSONArray jsonArray = new JSONArray(lastStr.toString());
            for (int i = 0; i < jsonArray.length(); ++i) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                BigDecimal lon = jsonObject.getBigDecimal("lon");
                BigDecimal lat = jsonObject.getBigDecimal("lat");
                endResult.put((new JSONArray()).put(lon).put(lat));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return endResult;
    }

    public static JSONArray formatUserLocation(JSONArray dataJson) {
        JSONArray jsonArray = new JSONArray();

        for (int i = 0; i < dataJson.length(); i = 0) {
            JSONObject object = new JSONObject();
            JSONObject jsonObject = dataJson.getJSONObject(i);
            object.put("f_user_name", jsonObject.getString("f_user_name"));
            object.put("f_user_id", jsonObject.getString("f_user_id"));
            String userid = jsonObject.getString("f_user_id");
            JSONArray tte = new JSONArray();
            int j = 0;

            while (j < dataJson.length()) {
                JSONObject j3 = dataJson.getJSONObject(j);
                if (j3.getString("f_user_id").equals(userid)) {
                    String f_start_date = j3.getString("f_start_date");
                    String f_end_date = j3.getString("f_end_date");
                    String f_user_name = j3.getString("f_user_name");
                    int idds = j3.getInt("id");
                    JSONObject te = (new JSONObject()).put("f_start_date", f_start_date).put("f_end_date", f_end_date).put("f_user_name", f_user_name).put("id", idds);
                    tte.put(te);
                    dataJson.remove(j);
                    j = 0;
                } else {
                    ++j;
                }
            }

            object.put("result", tte);
            jsonArray.put(object);
        }

        return jsonArray;
    }

    private static String getRealPath(String realpath) {
        Date d = new Date();
        realpath = realpath + File.separator + (d.getYear() + 1900) + File.separator + (d.getMonth() + 1) + File.separator + d.getDate();
        File f = new File(realpath);
        if (!f.exists()) {
            f.mkdirs();
        }
        return realpath;
    }

    public static String getPath(String pathname) throws IOException {
        String proPath = System.getProperty("user.dir");
        Properties pro = new Properties();
        try (FileInputStream in = new FileInputStream(proPath + "/file.properties")) {
            pro.load(in);
            String path = pro.getProperty(pathname);
            log.info("System.getProperty（”user.dir“）中的 {}节点值为{}", pathname, path);
            return path;
        }
    }
}
