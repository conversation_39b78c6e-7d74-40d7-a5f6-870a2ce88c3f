package com.af.v4.safecheck.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 新增限购请求对象
 */
public class AddLimitGasRequest {

    @JsonProperty("f_limit_style")
    private String fLimitStyle;

    @JsonProperty("f_time_type")
    private String fTimeType;

    @JsonProperty("f_limit_type")
    private String fLimitType;

    @JsonProperty("f_start_date")
    private String fStartDate;

    @JsonProperty("f_time_value")
    private String fTimeValue;

    @JsonProperty("f_third_pay")
    private String fThirdPay;

    @JsonProperty("f_limit_times")
    private String fLimitTimes;

    @JsonProperty("f_operate_date")
    private String fOperateDate;

    @JsonProperty("f_userinfo_id")
    private String fUserinfoId;

    @JsonProperty("f_resources_id")
    private String fResourcesId;

    @JsonProperty("f_resources_name")
    private String fResourcesName;

    // 无参构造函数
    public AddLimitGasRequest() {
    }

    // 全参构造函数
    public AddLimitGasRequest(String fLimitStyle, String fTimeType, String fLimitType,
                             String fStartDate, String fTimeValue, String fThirdPay,
                             String fLimitTimes, String fOperateDate, String fUserinfoId,
                             String fResourcesId, String fResourcesName) {
        this.fLimitStyle = fLimitStyle;
        this.fTimeType = fTimeType;
        this.fLimitType = fLimitType;
        this.fStartDate = fStartDate;
        this.fTimeValue = fTimeValue;
        this.fThirdPay = fThirdPay;
        this.fLimitTimes = fLimitTimes;
        this.fOperateDate = fOperateDate;
        this.fUserinfoId = fUserinfoId;
        this.fResourcesId = fResourcesId;
        this.fResourcesName = fResourcesName;
    }

    // Getter 和 Setter 方法
    public String getFLimitStyle() {
        return fLimitStyle;
    }

    public void setFLimitStyle(String fLimitStyle) {
        this.fLimitStyle = fLimitStyle;
    }

    public String getFTimeType() {
        return fTimeType;
    }

    public void setFTimeType(String fTimeType) {
        this.fTimeType = fTimeType;
    }

    public String getFLimitType() {
        return fLimitType;
    }

    public void setFLimitType(String fLimitType) {
        this.fLimitType = fLimitType;
    }

    public String getFStartDate() {
        return fStartDate;
    }

    public void setFStartDate(String fStartDate) {
        this.fStartDate = fStartDate;
    }

    public String getFTimeValue() {
        return fTimeValue;
    }

    public void setFTimeValue(String fTimeValue) {
        this.fTimeValue = fTimeValue;
    }

    public String getFThirdPay() {
        return fThirdPay;
    }

    public void setFThirdPay(String fThirdPay) {
        this.fThirdPay = fThirdPay;
    }

    public String getFLimitTimes() {
        return fLimitTimes;
    }

    public void setFLimitTimes(String fLimitTimes) {
        this.fLimitTimes = fLimitTimes;
    }

    public String getFOperateDate() {
        return fOperateDate;
    }

    public void setFOperateDate(String fOperateDate) {
        this.fOperateDate = fOperateDate;
    }

    public String getFUserinfoId() {
        return fUserinfoId;
    }

    public void setFUserinfoId(String fUserinfoId) {
        this.fUserinfoId = fUserinfoId;
    }

    public String getFResourcesId() {
        return fResourcesId;
    }

    public void setFResourcesId(String fResourcesId) {
        this.fResourcesId = fResourcesId;
    }

    public String getFResourcesName() {
        return fResourcesName;
    }

    public void setFResourcesName(String fResourcesName) {
        this.fResourcesName = fResourcesName;
    }

    @Override
    public String toString() {
        return "AddLimitGasRequest{" +
                "fLimitStyle='" + fLimitStyle + '\'' +
                ", fTimeType='" + fTimeType + '\'' +
                ", fLimitType='" + fLimitType + '\'' +
                ", fStartDate='" + fStartDate + '\'' +
                ", fTimeValue='" + fTimeValue + '\'' +
                ", fThirdPay='" + fThirdPay + '\'' +
                ", fLimitTimes='" + fLimitTimes + '\'' +
                ", fOperateDate='" + fOperateDate + '\'' +
                ", fUserinfoId='" + fUserinfoId + '\'' +
                ", fResourcesId='" + fResourcesId + '\'' +
                ", fResourcesName='" + fResourcesName + '\'' +
                '}';
    }
}
