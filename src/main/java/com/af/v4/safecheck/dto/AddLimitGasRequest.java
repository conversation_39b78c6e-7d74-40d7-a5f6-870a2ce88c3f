package com.af.v4.safecheck.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 新增限购请求对象
 */
public class AddLimitGasRequest {
    
    @JsonProperty("t.f_third_pay")
    private String tFThirdPay;
    
    @JsonProperty("t.f_limit_style")
    private String tFLimitStyle;
    
    @JsonProperty("t.f_limit_type")
    private String tFLimitType;
    
    @JsonProperty("t.f_time_type")
    private String tFTimeType;
    
    @JsonProperty("t.f_limit_times")
    private String tFLimitTimes;
    
    @JsonProperty("f_userinfo_id")
    private String fUserinfoId;
    
    @JsonProperty("f_resources_id")
    private String fResourcesId;
    
    @JsonProperty("f_resources_name")
    private String fResourcesName;

    // 无参构造函数
    public AddLimitGasRequest() {
    }

    // 全参构造函数
    public AddLimitGasRequest(String tFThirdPay, String tFLimitStyle, String tFLimitType, 
                             String tFTimeType, String tFLimitTimes, String fUserinfoId, 
                             String fResourcesId, String fResourcesName) {
        this.tFThirdPay = tFThirdPay;
        this.tFLimitStyle = tFLimitStyle;
        this.tFLimitType = tFLimitType;
        this.tFTimeType = tFTimeType;
        this.tFLimitTimes = tFLimitTimes;
        this.fUserinfoId = fUserinfoId;
        this.fResourcesId = fResourcesId;
        this.fResourcesName = fResourcesName;
    }

    // Getter 和 Setter 方法
    public String getTFThirdPay() {
        return tFThirdPay;
    }

    public void setTFThirdPay(String tFThirdPay) {
        this.tFThirdPay = tFThirdPay;
    }

    public String getTFLimitStyle() {
        return tFLimitStyle;
    }

    public void setTFLimitStyle(String tFLimitStyle) {
        this.tFLimitStyle = tFLimitStyle;
    }

    public String getTFLimitType() {
        return tFLimitType;
    }

    public void setTFLimitType(String tFLimitType) {
        this.tFLimitType = tFLimitType;
    }

    public String getTFTimeType() {
        return tFTimeType;
    }

    public void setTFTimeType(String tFTimeType) {
        this.tFTimeType = tFTimeType;
    }

    public String getTFLimitTimes() {
        return tFLimitTimes;
    }

    public void setTFLimitTimes(String tFLimitTimes) {
        this.tFLimitTimes = tFLimitTimes;
    }

    public String getFUserinfoId() {
        return fUserinfoId;
    }

    public void setFUserinfoId(String fUserinfoId) {
        this.fUserinfoId = fUserinfoId;
    }

    public String getFResourcesId() {
        return fResourcesId;
    }

    public void setFResourcesId(String fResourcesId) {
        this.fResourcesId = fResourcesId;
    }

    public String getFResourcesName() {
        return fResourcesName;
    }

    public void setFResourcesName(String fResourcesName) {
        this.fResourcesName = fResourcesName;
    }

    @Override
    public String toString() {
        return "AddLimitGasRequest{" +
                "tFThirdPay='" + tFThirdPay + '\'' +
                ", tFLimitStyle='" + tFLimitStyle + '\'' +
                ", tFLimitType='" + tFLimitType + '\'' +
                ", tFTimeType='" + tFTimeType + '\'' +
                ", tFLimitTimes='" + tFLimitTimes + '\'' +
                ", fUserinfoId='" + fUserinfoId + '\'' +
                ", fResourcesId='" + fResourcesId + '\'' +
                ", fResourcesName='" + fResourcesName + '\'' +
                '}';
    }
}
