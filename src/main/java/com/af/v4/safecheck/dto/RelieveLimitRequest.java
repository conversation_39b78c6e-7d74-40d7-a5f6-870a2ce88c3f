package com.af.v4.safecheck.dto;

/**
 * 解除限购请求对象
 */
public class RelieveLimitRequest {
    
    private String f_limit_id;
    private String f_relieve_remarks;
    private String remove;
    private String f_modify_user_id;
    private String f_modify_user_name;
    private String f_modify_date;
    private String f_limit_style;
    private String f_time_type;
    private String f_limit_type;

    // 无参构造函数
    public RelieveLimitRequest() {
    }

    // 全参构造函数
    public RelieveLimitRequest(String f_limit_id, String f_relieve_remarks, String remove, String f_modify_user_id,
                              String f_modify_user_name, String f_modify_date, String f_limit_style,
                              String f_time_type, String f_limit_type) {
        this.f_limit_id = f_limit_id;
        this.f_relieve_remarks = f_relieve_remarks;
        this.remove = remove;
        this.f_modify_user_id = f_modify_user_id;
        this.f_modify_user_name = f_modify_user_name;
        this.f_modify_date = f_modify_date;
        this.f_limit_style = f_limit_style;
        this.f_time_type = f_time_type;
        this.f_limit_type = f_limit_type;
    }

    // Getter 和 Setter 方法
    public String getF_limit_id() {
        return f_limit_id;
    }

    public void setF_limit_id(String f_limit_id) {
        this.f_limit_id = f_limit_id;
    }

    public String getF_relieve_remarks() {
        return f_relieve_remarks;
    }

    public void setF_relieve_remarks(String f_relieve_remarks) {
        this.f_relieve_remarks = f_relieve_remarks;
    }

    public String getRemove() {
        return remove;
    }

    public void setRemove(String remove) {
        this.remove = remove;
    }

    public String getF_modify_user_id() {
        return f_modify_user_id;
    }

    public void setF_modify_user_id(String f_modify_user_id) {
        this.f_modify_user_id = f_modify_user_id;
    }

    public String getF_modify_user_name() {
        return f_modify_user_name;
    }

    public void setF_modify_user_name(String f_modify_user_name) {
        this.f_modify_user_name = f_modify_user_name;
    }

    public String getF_modify_date() {
        return f_modify_date;
    }

    public void setF_modify_date(String f_modify_date) {
        this.f_modify_date = f_modify_date;
    }

    public String getF_limit_style() {
        return f_limit_style;
    }

    public void setF_limit_style(String f_limit_style) {
        this.f_limit_style = f_limit_style;
    }

    public String getF_time_type() {
        return f_time_type;
    }

    public void setF_time_type(String f_time_type) {
        this.f_time_type = f_time_type;
    }

    public String getF_limit_type() {
        return f_limit_type;
    }

    public void setF_limit_type(String f_limit_type) {
        this.f_limit_type = f_limit_type;
    }

    @Override
    public String toString() {
        return "RelieveLimitRequest{" +
                "f_limit_id='" + f_limit_id + '\'' +
                ", f_relieve_remarks='" + f_relieve_remarks + '\'' +
                ", remove='" + remove + '\'' +
                ", f_modify_user_id='" + f_modify_user_id + '\'' +
                ", f_modify_user_name='" + f_modify_user_name + '\'' +
                ", f_modify_date='" + f_modify_date + '\'' +
                ", f_limit_style='" + f_limit_style + '\'' +
                ", f_time_type='" + f_time_type + '\'' +
                ", f_limit_type='" + f_limit_type + '\'' +
                '}';
    }
}
