// 插件声明
plugins {
    id 'java'
    id 'org.springframework.boot' version "3.4.3"
    id 'io.spring.dependency-management' version '1.1.7'
}

// 自定义扩展变量配置
apply from: "config.gradle"
apply from: resources.text.fromInsecureUri("https://k8s.aofengcloud.com:31012/v4jar/dependencies-springboot3.gradle")

// 项目组
group = 'com.af.v4'
// 注意：master分支仅允许发布稳定版本
// 注意：develop分支仅允许发布快照版本

version = '2.0.71'

dependencies {
    /** 项目所需依赖 Start */
    // implementation 'cn.afterturn:easypoi-spring-boot-starter:4.5.0'
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'com.google.zxing:javase:3.5.3'
    implementation 'com.itextpdf:itextpdf:5.5.13.4'
    implementation "com.af.v4.system:af-common-job"
    implementation "com.af.v4.system:af-common-compatibility"
    implementation "com.af.v4.system:af-common-excel"
    implementation "com.af.v4.system:af-common-mobile"
    implementation "com.af.v4.system:af-common-task:${rootProject.ext.systemV4Version}"
    /** End */

    /** 派生依赖 Start，切勿自行修改，这些依赖由派生项目管理，请查阅 docs/V4架构文档/如何拉取最新的派生变动.md **/
    // 第三方依赖版本约束
    implementation platform("com.af.v4.system:af-dependencies:${rootProject.ext.dependenciesVersion}")
    // V4运行时
    implementation "com.af.v4.system:af-runtime:${rootProject.ext.systemV4Version}"
    // 实体版本
    implementation "com.af.v4:af-entity-lib:${rootProject.ext.entityLibVersion}"
    // 热更新工具
    // developmentOnly "org.springframework.boot:spring-boot-devtools"
    // 单元测试相关
    testImplementation ('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'com.vaadin.external.google', module: 'android-json'
    }
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    /** End **/
}

// 依赖源配置
repositories {
    // 本地maven源
    mavenLocal()
    // 公司maven源
    maven {
        allowInsecureProtocol = true
        url rootProject.ext.mavenUrl
    }
    // 公司快照源
    maven {
        allowInsecureProtocol = true
        url rootProject.ext.snapshotsUrl
    }
    // 公司旧maven源
    maven {
        allowInsecureProtocol = true
        url rootProject.ext.mavenOldUrl
    }
    // 阿里云源
    maven { url 'https://maven.aliyun.com/repository/central'}
    // Spring源
    maven { url "https://repo.springsource.org/libs-milestone-local"}
    // 官方源
    maven { url "https://mvnrepository.com/artifact/"}
    // Spring Ai源
    maven { url "https://repo.spring.io/milestone"}
    mavenCentral()
}

// JDK版本
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}
// 避免java编译时缺省状态因为中文字符而失败
[compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'

// 移除默认的logback
configurations.implementation {
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    exclude group: 'commons-logging', module: 'commons-logging'
}

jar.enabled = false
// 打包为 bootJar，可独立运行
bootJar {
    enabled = true
    archiveFileName = "${archiveBaseName.get()}-boot.${archiveExtension.get()}"
    archiveClassifier = 'boot'
    manifest {
        attributes 'Implementation-Version': version
    }
}

// 发布标签
tasks.register('pushTag') {
    doLast {
        def version = project.version
        def tag = "v${version}"
        exec {
            commandLine 'git', 'tag' , '-a', tag , '-m', '"Release version ' + version + '"'
        }
        exec {
            commandLine 'git', 'push', 'origin', tag
        }
    }
}

import groovy.json.JsonBuilder

// 生成tenants.json
tasks.register('createTenantsJson') {
    doLast {
        // 定义资源目录和目标文件
        def resourcesDir = 'src/main/resources'
        def tenantsDir = new File(resourcesDir, 'tenants')
        def outputFile = new File(resourcesDir, 'tenants.json')

        // 确保tenants目录存在
        if (!tenantsDir.exists()) {
            outputFile.text = "[]"
            return
        }

        // 获取tenants目录下的所有文件夹
        def folders = tenantsDir.listFiles({ file -> file.isDirectory() } as FileFilter)

        // 使用JsonBuilder构建JSON
        def jsonBuilder = new JsonBuilder(folders.collect { it.name })
        outputFile.text = jsonBuilder.toPrettyString()
    }
}

tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        showStandardStreams = true
    }
}

// 确保在处理资源之前执行createTenantsJson任务
processResources.dependsOn 'createTenantsJson'

// 将生成的tenants.json文件包括在资源中
processResources {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    from('src/main/resources/tenants.json')
    into('build/resources/main')
}
